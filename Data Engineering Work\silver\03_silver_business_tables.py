# Databricks notebook source
# MAGIC %md
# MAGIC # Silver Layer - Business-Specific Tables
# MAGIC 
# MAGIC This notebook creates business-specific tables in the silver layer that provide real business value.
# MAGIC These tables combine multiple data sources and apply business logic to create meaningful datasets.

# COMMAND ----------

# MAGIC %md
# MAGIC ## 1. Setup and Configuration

# COMMAND ----------

from pyspark.sql import SparkSession
from pyspark.sql.functions import *
from pyspark.sql.types import *
from delta.tables import DeltaTable
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get configuration
catalog_name = spark.conf.get("ecommerce.config.catalog_name", "ecommerce")
bronze_schema = spark.conf.get("ecommerce.config.bronze_schema", "ecommerce.bronze")
silver_schema = spark.conf.get("ecommerce.config.silver_schema", "ecommerce.silver")

# Use the ecommerce catalog
spark.sql(f"USE CATALOG {catalog_name}")

print(f"🔧 Creating business tables in: {silver_schema}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## 2. Order Items Fact Table with Business Metrics

# COMMAND ----------

def create_silver_order_items():
    """Create comprehensive order items fact table with business metrics"""
    
    # Read from bronze tables
    bronze_order_items = spark.table(f"{bronze_schema}.order_items")
    bronze_orders = spark.table(f"{bronze_schema}.orders")
    bronze_products = spark.table(f"{bronze_schema}.products")
    bronze_sellers = spark.table(f"{bronze_schema}.sellers")
    
    # Join all related tables
    silver_order_items = bronze_order_items.alias("oi") \
        .join(bronze_orders.alias("o"), col("oi.order_id") == col("o.order_id"), "inner") \
        .join(bronze_products.alias("p"), col("oi.product_id") == col("p.product_id"), "left") \
        .join(bronze_sellers.alias("s"), col("oi.seller_id") == col("s.seller_id"), "left")
    
    # Select and transform columns
    silver_order_items = silver_order_items.select(
        col("oi.order_id").alias("order_key"),
        col("oi.order_item_id").alias("order_item_sequence"),
        col("oi.product_id").alias("product_key"),
        col("oi.seller_id").alias("seller_key"),
        col("o.customer_id").alias("customer_key"),
        col("o.order_status").alias("order_status"),
        to_timestamp(col("o.order_purchase_timestamp")).alias("order_purchase_datetime"),
        to_timestamp(col("oi.shipping_limit_date")).alias("shipping_limit_datetime"),
        col("oi.price").alias("item_price"),
        col("oi.freight_value").alias("freight_cost"),
        col("p.product_category_name").alias("product_category"),
        col("p.product_weight_g").alias("product_weight_grams"),
        col("s.seller_state").alias("seller_state"),
        col("o.order_delivered_customer_date").alias("delivery_date")
    )
    
    # Add business calculations
    silver_order_items = silver_order_items.withColumn(
        "total_item_value", col("item_price") + col("freight_cost")
    ).withColumn(
        "order_year", year(col("order_purchase_datetime"))
    ).withColumn(
        "order_month", month(col("order_purchase_datetime"))
    ).withColumn(
        "order_quarter", quarter(col("order_purchase_datetime"))
    ).withColumn(
        "is_delivered", 
        when(col("order_status") == "delivered", 1).otherwise(0)
    ).withColumn(
        "revenue_category",
        when(col("item_price") < 50, "Low Value")
        .when(col("item_price") < 200, "Medium Value")
        .when(col("item_price") < 500, "High Value")
        .otherwise("Premium")
    )
    
    # Add data quality and metadata
    silver_order_items = silver_order_items.withColumn(
        "_silver_processed_timestamp", current_timestamp()
    ).withColumn(
        "_silver_data_quality_score",
        when(col("order_key").isNull() | col("product_key").isNull(), 0.0)
        .when(col("item_price").isNull(), 0.5)
        .otherwise(1.0)
    )
    
    # Write to silver table
    silver_order_items.write \
        .format("delta") \
        .mode("overwrite") \
        .option("mergeSchema", "true") \
        .saveAsTable(f"{silver_schema}.fact_order_items")
    
    record_count = silver_order_items.count()
    logger.info(f"✅ Created silver order items fact table with {record_count} records")
    return record_count

# Execute order items transformation
order_items_count = create_silver_order_items()

# COMMAND ----------

# MAGIC %md
# MAGIC ## 3. Customer Payment Behavior Table

# COMMAND ----------

def create_silver_payment_behavior():
    """Create customer payment behavior analysis table"""
    
    # Read from bronze tables
    bronze_payments = spark.table(f"{bronze_schema}.order_payments")
    bronze_orders = spark.table(f"{bronze_schema}.orders")
    
    # Join payments with orders
    payment_behavior = bronze_payments.alias("p") \
        .join(bronze_orders.alias("o"), col("p.order_id") == col("o.order_id"), "inner")
    
    # Aggregate payment behavior by customer
    customer_payment_behavior = payment_behavior.groupBy(
        col("o.customer_id").alias("customer_key")
    ).agg(
        count("p.order_id").alias("total_orders"),
        countDistinct("p.payment_type").alias("unique_payment_methods"),
        sum("p.payment_value").alias("total_payment_value"),
        avg("p.payment_value").alias("avg_payment_value"),
        max("p.payment_value").alias("max_payment_value"),
        avg("p.payment_installments").alias("avg_installments"),
        max("p.payment_installments").alias("max_installments"),
        
        # Payment method preferences
        sum(when(col("p.payment_type") == "credit_card", col("p.payment_value")).otherwise(0)).alias("credit_card_value"),
        sum(when(col("p.payment_type") == "boleto", col("p.payment_value")).otherwise(0)).alias("boleto_value"),
        sum(when(col("p.payment_type") == "voucher", col("p.payment_value")).otherwise(0)).alias("voucher_value"),
        sum(when(col("p.payment_type") == "debit_card", col("p.payment_value")).otherwise(0)).alias("debit_card_value"),
        
        # Payment method counts
        sum(when(col("p.payment_type") == "credit_card", 1).otherwise(0)).alias("credit_card_orders"),
        sum(when(col("p.payment_type") == "boleto", 1).otherwise(0)).alias("boleto_orders"),
        sum(when(col("p.payment_type") == "voucher", 1).otherwise(0)).alias("voucher_orders"),
        sum(when(col("p.payment_type") == "debit_card", 1).otherwise(0)).alias("debit_card_orders")
    )
    
    # Add derived metrics
    customer_payment_behavior = customer_payment_behavior.withColumn(
        "preferred_payment_method",
        when(col("credit_card_orders") >= col("boleto_orders") & 
             col("credit_card_orders") >= col("voucher_orders") & 
             col("credit_card_orders") >= col("debit_card_orders"), "credit_card")
        .when(col("boleto_orders") >= col("voucher_orders") & 
              col("boleto_orders") >= col("debit_card_orders"), "boleto")
        .when(col("voucher_orders") >= col("debit_card_orders"), "voucher")
        .otherwise("debit_card")
    ).withColumn(
        "payment_diversity_score",
        col("unique_payment_methods") / 4.0  # Normalize by max possible payment methods
    ).withColumn(
        "installment_preference",
        when(col("avg_installments") <= 1.5, "Cash Buyer")
        .when(col("avg_installments") <= 3, "Short Term")
        .when(col("avg_installments") <= 6, "Medium Term")
        .otherwise("Long Term")
    ).withColumn(
        "customer_value_segment",
        when(col("total_payment_value") < 100, "Low Value")
        .when(col("total_payment_value") < 500, "Medium Value")
        .when(col("total_payment_value") < 1500, "High Value")
        .otherwise("VIP")
    )
    
    # Add metadata
    customer_payment_behavior = customer_payment_behavior.withColumn(
        "_silver_processed_timestamp", current_timestamp()
    ).withColumn(
        "_silver_data_quality_score", lit(1.0)
    )
    
    # Write to silver table
    customer_payment_behavior.write \
        .format("delta") \
        .mode("overwrite") \
        .option("mergeSchema", "true") \
        .saveAsTable(f"{silver_schema}.customer_payment_behavior")
    
    record_count = customer_payment_behavior.count()
    logger.info(f"✅ Created customer payment behavior table with {record_count} records")
    return record_count

# Execute payment behavior transformation
payment_behavior_count = create_silver_payment_behavior()

# COMMAND ----------

# MAGIC %md
# MAGIC ## 4. Product Performance Table

# COMMAND ----------

def create_silver_product_performance():
    """Create product performance analysis table"""
    
    # Read from silver tables
    order_items = spark.table(f"{silver_schema}.fact_order_items")
    bronze_reviews = spark.table(f"{bronze_schema}.order_reviews")
    
    # Aggregate product performance metrics
    product_performance = order_items.groupBy(
        col("product_key"),
        col("product_category")
    ).agg(
        count("order_key").alias("total_orders"),
        countDistinct("order_key").alias("unique_orders"),
        countDistinct("customer_key").alias("unique_customers"),
        countDistinct("seller_key").alias("unique_sellers"),
        sum("item_price").alias("total_revenue"),
        avg("item_price").alias("avg_price"),
        sum("freight_cost").alias("total_freight"),
        avg("freight_cost").alias("avg_freight"),
        sum("total_item_value").alias("total_value"),
        sum(col("is_delivered")).alias("delivered_orders"),
        min("order_purchase_datetime").alias("first_sale_date"),
        max("order_purchase_datetime").alias("last_sale_date")
    )
    
    # Add performance metrics
    product_performance = product_performance.withColumn(
        "delivery_rate", 
        col("delivered_orders") / col("total_orders")
    ).withColumn(
        "avg_order_value",
        col("total_value") / col("total_orders")
    ).withColumn(
        "revenue_per_customer",
        col("total_revenue") / col("unique_customers")
    ).withColumn(
        "days_in_catalog",
        datediff(col("last_sale_date"), col("first_sale_date"))
    ).withColumn(
        "sales_velocity",
        col("total_orders") / greatest(col("days_in_catalog"), lit(1))
    )
    
    # Add product performance categories
    product_performance = product_performance.withColumn(
        "performance_tier",
        when(col("total_revenue") >= 10000, "Top Performer")
        .when(col("total_revenue") >= 5000, "High Performer")
        .when(col("total_revenue") >= 1000, "Medium Performer")
        .when(col("total_revenue") >= 100, "Low Performer")
        .otherwise("New/Inactive")
    ).withColumn(
        "popularity_score",
        (col("unique_customers") * 0.4 + col("total_orders") * 0.6) / 100
    )
    
    # Join with review data for quality metrics
    review_metrics = bronze_reviews.groupBy("order_id").agg(
        avg("review_score").alias("avg_review_score"),
        count("review_id").alias("review_count")
    )
    
    order_reviews = order_items.alias("oi").join(
        review_metrics.alias("r"),
        col("oi.order_key") == col("r.order_id"),
        "left"
    ).groupBy("oi.product_key").agg(
        avg("r.avg_review_score").alias("product_avg_rating"),
        sum("r.review_count").alias("total_reviews")
    )
    
    # Join product performance with review metrics
    product_performance = product_performance.alias("pp").join(
        order_reviews.alias("or"),
        col("pp.product_key") == col("or.product_key"),
        "left"
    ).select(
        col("pp.*"),
        coalesce(col("or.product_avg_rating"), lit(0.0)).alias("avg_rating"),
        coalesce(col("or.total_reviews"), lit(0)).alias("total_reviews")
    )
    
    # Add final quality score
    product_performance = product_performance.withColumn(
        "quality_score",
        when(col("avg_rating") >= 4.5, "Excellent")
        .when(col("avg_rating") >= 4.0, "Good")
        .when(col("avg_rating") >= 3.5, "Average")
        .when(col("avg_rating") >= 3.0, "Below Average")
        .when(col("avg_rating") > 0, "Poor")
        .otherwise("No Reviews")
    )
    
    # Add metadata
    product_performance = product_performance.withColumn(
        "_silver_processed_timestamp", current_timestamp()
    ).withColumn(
        "_silver_data_quality_score", lit(1.0)
    )
    
    # Write to silver table
    product_performance.write \
        .format("delta") \
        .mode("overwrite") \
        .option("mergeSchema", "true") \
        .saveAsTable(f"{silver_schema}.product_performance")
    
    record_count = product_performance.count()
    logger.info(f"✅ Created product performance table with {record_count} records")
    return record_count

# Execute product performance transformation
product_performance_count = create_silver_product_performance()

# COMMAND ----------

# MAGIC %md
# MAGIC ## 5. Seller Performance Table

# COMMAND ----------

def create_silver_seller_performance():
    """Create seller performance analysis table"""
    
    # Read from silver tables
    order_items = spark.table(f"{silver_schema}.fact_order_items")
    
    # Aggregate seller performance metrics
    seller_performance = order_items.groupBy(
        col("seller_key"),
        col("seller_state")
    ).agg(
        count("order_key").alias("total_orders"),
        countDistinct("order_key").alias("unique_orders"),
        countDistinct("customer_key").alias("unique_customers"),
        countDistinct("product_key").alias("unique_products"),
        countDistinct("product_category").alias("unique_categories"),
        sum("item_price").alias("total_revenue"),
        avg("item_price").alias("avg_item_price"),
        sum("freight_cost").alias("total_freight_revenue"),
        sum("total_item_value").alias("total_value"),
        sum(col("is_delivered")).alias("delivered_orders"),
        min("order_purchase_datetime").alias("first_sale_date"),
        max("order_purchase_datetime").alias("last_sale_date")
    )
    
    # Add performance metrics
    seller_performance = seller_performance.withColumn(
        "delivery_success_rate",
        col("delivered_orders") / col("total_orders")
    ).withColumn(
        "avg_order_value",
        col("total_value") / col("total_orders")
    ).withColumn(
        "revenue_per_customer",
        col("total_revenue") / col("unique_customers")
    ).withColumn(
        "product_diversity_score",
        col("unique_categories") / 10.0  # Normalize by reasonable max categories
    ).withColumn(
        "days_active",
        datediff(col("last_sale_date"), col("first_sale_date"))
    ).withColumn(
        "sales_velocity",
        col("total_orders") / greatest(col("days_active"), lit(1))
    )
    
    # Add seller performance tiers
    seller_performance = seller_performance.withColumn(
        "seller_tier",
        when(col("total_revenue") >= 50000, "Platinum")
        .when(col("total_revenue") >= 20000, "Gold")
        .when(col("total_revenue") >= 5000, "Silver")
        .when(col("total_revenue") >= 1000, "Bronze")
        .otherwise("Starter")
    ).withColumn(
        "business_maturity",
        when(col("days_active") >= 365, "Established")
        .when(col("days_active") >= 180, "Growing")
        .when(col("days_active") >= 90, "Developing")
        .otherwise("New")
    )
    
    # Add metadata
    seller_performance = seller_performance.withColumn(
        "_silver_processed_timestamp", current_timestamp()
    ).withColumn(
        "_silver_data_quality_score", lit(1.0)
    )
    
    # Write to silver table
    seller_performance.write \
        .format("delta") \
        .mode("overwrite") \
        .option("mergeSchema", "true") \
        .saveAsTable(f"{silver_schema}.seller_performance")
    
    record_count = seller_performance.count()
    logger.info(f"✅ Created seller performance table with {record_count} records")
    return record_count

# Execute seller performance transformation
seller_performance_count = create_silver_seller_performance()

# COMMAND ----------

print("🎉 Silver layer business tables completed successfully!")
print(f"📊 Created business tables:")
print(f"   - fact_order_items: {order_items_count:,} records")
print(f"   - customer_payment_behavior: {payment_behavior_count:,} records")
print(f"   - product_performance: {product_performance_count:,} records")
print(f"   - seller_performance: {seller_performance_count:,} records")
print(f"\n📋 Next steps:")
print("   1. Create gold layer aggregated tables")
print("   2. Build ML feature store tables")
print("   3. Implement data quality monitoring")
