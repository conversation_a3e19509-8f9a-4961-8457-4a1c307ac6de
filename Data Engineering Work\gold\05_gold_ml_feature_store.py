# Databricks notebook source
# MAGIC %md
# MAGIC # Gold Layer - ML Feature Store for Data Science Team
# MAGIC 
# MAGIC This notebook creates ML-ready feature tables optimized for machine learning models.
# MAGIC Features are engineered for common ML use cases: churn prediction, demand forecasting, and recommendation systems.

# COMMAND ----------

# MAGIC %md
# MAGIC ## 1. Setup and Configuration

# COMMAND ----------

from pyspark.sql import SparkSession
from pyspark.sql.functions import *
from pyspark.sql.types import *
from pyspark.sql.window import Window
from delta.tables import DeltaTable
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get configuration
catalog_name = spark.conf.get("ecommerce.config.catalog_name", "ecommerce")
silver_schema = spark.conf.get("ecommerce.config.silver_schema", "ecommerce.silver")
gold_schema = spark.conf.get("ecommerce.config.gold_schema", "ecommerce.gold")

# Use the ecommerce catalog
spark.sql(f"USE CATALOG {catalog_name}")

print(f"🔧 Creating ML feature tables in: {gold_schema}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## 2. Customer Churn Prediction Features

# COMMAND ----------

def create_customer_churn_features():
    """Create feature table for customer churn prediction models"""
    
    # Read from silver tables
    order_items = spark.table(f"{silver_schema}.fact_order_items")
    payment_behavior = spark.table(f"{silver_schema}.customer_payment_behavior")
    customers = spark.table(f"{silver_schema}.dim_customers")
    
    # Define observation window (last 6 months of data)
    observation_cutoff = lit("2018-06-30")
    prediction_window_start = lit("2018-07-01")
    prediction_window_end = lit("2018-12-31")
    
    # Calculate customer features up to observation cutoff
    customer_features = order_items.filter(
        (col("order_purchase_datetime") <= observation_cutoff) &
        (col("order_status") == "delivered")
    ).groupBy("customer_key").agg(
        # Recency features
        datediff(observation_cutoff, max("order_purchase_datetime")).alias("days_since_last_order"),
        datediff(observation_cutoff, min("order_purchase_datetime")).alias("customer_age_days"),
        
        # Frequency features
        count("order_key").alias("total_orders"),
        countDistinct("order_key").alias("unique_orders"),
        avg(datediff(col("order_purchase_datetime"), lag("order_purchase_datetime").over(
            Window.partitionBy("customer_key").orderBy("order_purchase_datetime")
        ))).alias("avg_days_between_orders"),
        
        # Monetary features
        sum("total_item_value").alias("total_spent"),
        avg("total_item_value").alias("avg_order_value"),
        stddev("total_item_value").alias("order_value_std"),
        min("total_item_value").alias("min_order_value"),
        max("total_item_value").alias("max_order_value"),
        
        # Product diversity features
        countDistinct("product_key").alias("unique_products_purchased"),
        countDistinct("product_category").alias("unique_categories_purchased"),
        countDistinct("seller_key").alias("unique_sellers_used"),
        
        # Temporal features
        countDistinct(month("order_purchase_datetime")).alias("active_months"),
        countDistinct(quarter("order_purchase_datetime")).alias("active_quarters"),
        
        # Last order features
        last("revenue_category").alias("last_order_value_category"),
        last("product_category").alias("last_product_category")
    )
    
    # Calculate derived features
    customer_features = customer_features.withColumn(
        "order_frequency_score", 
        col("total_orders") / greatest(col("customer_age_days") / 30.0, lit(1))
    ).withColumn(
        "spending_consistency",
        when(col("order_value_std").isNull() | (col("order_value_std") == 0), 1.0)
        .otherwise(col("avg_order_value") / col("order_value_std"))
    ).withColumn(
        "product_exploration_score",
        col("unique_products_purchased") / greatest(col("total_orders"), lit(1))
    ).withColumn(
        "seller_loyalty_score",
        1.0 / greatest(col("unique_sellers_used"), lit(1))
    ).withColumn(
        "engagement_score",
        col("active_months") / greatest(col("customer_age_days") / 30.0, lit(1))
    )
    
    # Join with payment behavior features
    customer_features = customer_features.alias("cf").join(
        payment_behavior.alias("pb"),
        col("cf.customer_key") == col("pb.customer_key"),
        "left"
    ).select(
        col("cf.*"),
        coalesce(col("pb.preferred_payment_method"), lit("unknown")).alias("preferred_payment_method"),
        coalesce(col("pb.avg_installments"), lit(1.0)).alias("avg_installments"),
        coalesce(col("pb.payment_diversity_score"), lit(0.0)).alias("payment_diversity_score")
    )
    
    # Join with customer demographics
    customer_features = customer_features.alias("cf").join(
        customers.alias("c"),
        col("cf.customer_key") == col("c.customer_key"),
        "left"
    ).select(
        col("cf.*"),
        col("c.state_code").alias("customer_state"),
        col("c.city").alias("customer_city")
    )
    
    # Create churn label (did customer make purchase in prediction window?)
    future_orders = order_items.filter(
        (col("order_purchase_datetime") >= prediction_window_start) &
        (col("order_purchase_datetime") <= prediction_window_end)
    ).select("customer_key").distinct().withColumn("made_future_purchase", lit(1))
    
    # Join with future purchase indicator
    churn_features = customer_features.alias("cf").join(
        future_orders.alias("fo"),
        col("cf.customer_key") == col("fo.customer_key"),
        "left"
    ).select(
        col("cf.*"),
        when(col("fo.made_future_purchase").isNull(), 1).otherwise(0).alias("churned")
    )
    
    # Add feature engineering timestamp
    churn_features = churn_features.withColumn(
        "feature_timestamp", lit(observation_cutoff)
    ).withColumn(
        "_gold_created_timestamp", current_timestamp()
    ).withColumn(
        "_gold_table_type", lit("ml_features_churn")
    )
    
    # Write to gold table
    churn_features.write \
        .format("delta") \
        .mode("overwrite") \
        .option("mergeSchema", "true") \
        .saveAsTable(f"{gold_schema}.ml_customer_churn_features")
    
    record_count = churn_features.count()
    churn_rate = churn_features.agg(avg("churned")).collect()[0][0]
    logger.info(f"✅ Created customer churn features with {record_count} records, churn rate: {churn_rate:.2%}")
    return record_count

# Execute churn features
churn_features_count = create_customer_churn_features()

# COMMAND ----------

# MAGIC %md
# MAGIC ## 3. Product Demand Forecasting Features

# COMMAND ----------

def create_demand_forecasting_features():
    """Create feature table for product demand forecasting models"""
    
    # Read from silver tables
    order_items = spark.table(f"{silver_schema}.fact_order_items")
    product_performance = spark.table(f"{silver_schema}.product_performance")
    
    # Create weekly aggregations for time series forecasting
    weekly_demand = order_items.filter(
        col("order_status") == "delivered"
    ).withColumn(
        "year_week", concat(col("order_year"), lit("-"), weekofyear("order_purchase_datetime"))
    ).withColumn(
        "week_start_date", date_sub(col("order_purchase_datetime"), dayofweek("order_purchase_datetime") - 1)
    ).groupBy(
        "product_key",
        "product_category", 
        "year_week",
        "week_start_date",
        col("order_year").alias("year"),
        weekofyear("order_purchase_datetime").alias("week_of_year")
    ).agg(
        # Demand metrics
        count("order_key").alias("weekly_orders"),
        sum("item_price").alias("weekly_revenue"),
        avg("item_price").alias("avg_weekly_price"),
        countDistinct("customer_key").alias("unique_customers"),
        countDistinct("seller_key").alias("unique_sellers"),
        
        # Price metrics
        min("item_price").alias("min_price"),
        max("item_price").alias("max_price"),
        stddev("item_price").alias("price_volatility")
    )
    
    # Add lag features for time series
    window_spec = Window.partitionBy("product_key").orderBy("week_start_date")
    
    weekly_demand = weekly_demand.withColumn(
        "weekly_orders_lag1", lag("weekly_orders", 1).over(window_spec)
    ).withColumn(
        "weekly_orders_lag2", lag("weekly_orders", 2).over(window_spec)
    ).withColumn(
        "weekly_orders_lag4", lag("weekly_orders", 4).over(window_spec)
    ).withColumn(
        "weekly_revenue_lag1", lag("weekly_revenue", 1).over(window_spec)
    ).withColumn(
        "weekly_revenue_lag4", lag("weekly_revenue", 4).over(window_spec)
    )
    
    # Add moving averages
    weekly_demand = weekly_demand.withColumn(
        "orders_ma_4weeks", 
        avg("weekly_orders").over(window_spec.rowsBetween(-3, 0))
    ).withColumn(
        "revenue_ma_4weeks",
        avg("weekly_revenue").over(window_spec.rowsBetween(-3, 0))
    ).withColumn(
        "orders_ma_8weeks",
        avg("weekly_orders").over(window_spec.rowsBetween(-7, 0))
    )
    
    # Add trend features
    weekly_demand = weekly_demand.withColumn(
        "orders_trend_4weeks",
        (col("weekly_orders") - col("orders_ma_4weeks")) / greatest(col("orders_ma_4weeks"), lit(1))
    ).withColumn(
        "revenue_trend_4weeks", 
        (col("weekly_revenue") - col("revenue_ma_4weeks")) / greatest(col("revenue_ma_4weeks"), lit(1))
    )
    
    # Add seasonal features
    weekly_demand = weekly_demand.withColumn(
        "month", month("week_start_date")
    ).withColumn(
        "quarter", quarter("week_start_date")
    ).withColumn(
        "is_holiday_season",
        when(col("month").isin(11, 12), 1).otherwise(0)
    ).withColumn(
        "is_summer_season",
        when(col("month").isin(12, 1, 2), 1).otherwise(0)  # Southern hemisphere
    )
    
    # Join with product characteristics
    demand_features = weekly_demand.alias("wd").join(
        product_performance.select(
            "product_key", "performance_tier", "avg_rating", "total_reviews"
        ).alias("pp"),
        col("wd.product_key") == col("pp.product_key"),
        "left"
    ).select(
        col("wd.*"),
        coalesce(col("pp.performance_tier"), lit("Unknown")).alias("performance_tier"),
        coalesce(col("pp.avg_rating"), lit(0.0)).alias("product_rating"),
        coalesce(col("pp.total_reviews"), lit(0)).alias("total_reviews")
    )
    
    # Add metadata
    demand_features = demand_features.withColumn(
        "_gold_created_timestamp", current_timestamp()
    ).withColumn(
        "_gold_table_type", lit("ml_features_demand")
    )
    
    # Write to gold table
    demand_features.write \
        .format("delta") \
        .mode("overwrite") \
        .option("mergeSchema", "true") \
        .saveAsTable(f"{gold_schema}.ml_demand_forecasting_features")
    
    record_count = demand_features.count()
    logger.info(f"✅ Created demand forecasting features with {record_count} records")
    return record_count

# Execute demand forecasting features
demand_features_count = create_demand_forecasting_features()

# COMMAND ----------

# MAGIC %md
# MAGIC ## 4. Product Recommendation Features

# COMMAND ----------

def create_recommendation_features():
    """Create feature table for product recommendation systems"""
    
    # Read from silver tables
    order_items = spark.table(f"{silver_schema}.fact_order_items")
    
    # Create customer-product interaction matrix
    customer_product_features = order_items.filter(
        col("order_status") == "delivered"
    ).groupBy("customer_key", "product_key", "product_category").agg(
        count("order_key").alias("purchase_frequency"),
        sum("total_item_value").alias("total_spent_on_product"),
        avg("total_item_value").alias("avg_spent_per_order"),
        max("order_purchase_datetime").alias("last_purchase_date"),
        min("order_purchase_datetime").alias("first_purchase_date")
    )
    
    # Add customer-level features
    customer_totals = order_items.filter(
        col("order_status") == "delivered"
    ).groupBy("customer_key").agg(
        count("order_key").alias("customer_total_orders"),
        sum("total_item_value").alias("customer_total_spent"),
        countDistinct("product_category").alias("customer_category_diversity")
    )
    
    # Add product-level features
    product_totals = order_items.filter(
        col("order_status") == "delivered"
    ).groupBy("product_key").agg(
        count("order_key").alias("product_total_orders"),
        countDistinct("customer_key").alias("product_unique_customers"),
        avg("total_item_value").alias("product_avg_price")
    )
    
    # Join all features
    recommendation_features = customer_product_features.alias("cpf") \
        .join(customer_totals.alias("ct"), col("cpf.customer_key") == col("ct.customer_key"), "left") \
        .join(product_totals.alias("pt"), col("cpf.product_key") == col("pt.product_key"), "left")
    
    # Calculate recommendation scores
    recommendation_features = recommendation_features.select(
        col("cpf.*"),
        col("ct.customer_total_orders"),
        col("ct.customer_total_spent"),
        col("ct.customer_category_diversity"),
        col("pt.product_total_orders"),
        col("pt.product_unique_customers"),
        col("pt.product_avg_price")
    ).withColumn(
        "product_affinity_score",
        col("purchase_frequency") / col("customer_total_orders")
    ).withColumn(
        "spending_share_on_product",
        col("total_spent_on_product") / col("customer_total_spent")
    ).withColumn(
        "product_popularity_score",
        col("product_unique_customers") / lit(100000.0)  # Normalize by total customers
    ).withColumn(
        "price_sensitivity_score",
        col("avg_spent_per_order") / col("product_avg_price")
    ).withColumn(
        "recency_score",
        datediff(lit("2018-12-31"), col("last_purchase_date"))
    )
    
    # Create collaborative filtering features
    # Find customers who bought similar products
    similar_customers = order_items.alias("oi1").join(
        order_items.alias("oi2"),
        (col("oi1.product_key") == col("oi2.product_key")) & 
        (col("oi1.customer_key") != col("oi2.customer_key")),
        "inner"
    ).groupBy(
        col("oi1.customer_key").alias("customer_a"),
        col("oi2.customer_key").alias("customer_b")
    ).agg(
        countDistinct("oi1.product_key").alias("common_products")
    ).filter(col("common_products") >= 2)
    
    # Add metadata
    recommendation_features = recommendation_features.withColumn(
        "_gold_created_timestamp", current_timestamp()
    ).withColumn(
        "_gold_table_type", lit("ml_features_recommendation")
    )
    
    # Write to gold table
    recommendation_features.write \
        .format("delta") \
        .mode("overwrite") \
        .option("mergeSchema", "true") \
        .saveAsTable(f"{gold_schema}.ml_recommendation_features")
    
    record_count = recommendation_features.count()
    logger.info(f"✅ Created recommendation features with {record_count} records")
    return record_count

# Execute recommendation features
recommendation_features_count = create_recommendation_features()

# COMMAND ----------

# MAGIC %md
# MAGIC ## 5. Seller Performance Prediction Features

# COMMAND ----------

def create_seller_performance_features():
    """Create feature table for seller performance prediction"""
    
    # Read from silver tables
    order_items = spark.table(f"{silver_schema}.fact_order_items")
    seller_performance = spark.table(f"{silver_schema}.seller_performance")
    
    # Create monthly seller performance features
    monthly_seller_features = order_items.filter(
        col("order_status") == "delivered"
    ).withColumn(
        "year_month", concat(col("order_year"), lit("-"), 
                           when(col("order_month") < 10, concat(lit("0"), col("order_month")))
                           .otherwise(col("order_month")))
    ).groupBy(
        "seller_key",
        "year_month",
        col("order_year").alias("year"),
        col("order_month").alias("month")
    ).agg(
        # Performance metrics
        count("order_key").alias("monthly_orders"),
        sum("total_item_value").alias("monthly_revenue"),
        avg("total_item_value").alias("avg_order_value"),
        countDistinct("customer_key").alias("unique_customers"),
        countDistinct("product_key").alias("unique_products"),
        countDistinct("product_category").alias("unique_categories"),
        
        # Quality metrics
        avg("item_price").alias("avg_item_price"),
        stddev("item_price").alias("price_consistency")
    )
    
    # Add lag features for seller performance prediction
    window_spec = Window.partitionBy("seller_key").orderBy("year_month")
    
    monthly_seller_features = monthly_seller_features.withColumn(
        "monthly_orders_lag1", lag("monthly_orders", 1).over(window_spec)
    ).withColumn(
        "monthly_revenue_lag1", lag("monthly_revenue", 1).over(window_spec)
    ).withColumn(
        "monthly_orders_lag3", lag("monthly_orders", 3).over(window_spec)
    ).withColumn(
        "monthly_revenue_lag3", lag("monthly_revenue", 3).over(window_spec)
    )
    
    # Add growth metrics
    monthly_seller_features = monthly_seller_features.withColumn(
        "orders_growth_rate",
        when(col("monthly_orders_lag1") > 0,
             (col("monthly_orders") - col("monthly_orders_lag1")) / col("monthly_orders_lag1"))
        .otherwise(0)
    ).withColumn(
        "revenue_growth_rate",
        when(col("monthly_revenue_lag1") > 0,
             (col("monthly_revenue") - col("monthly_revenue_lag1")) / col("monthly_revenue_lag1"))
        .otherwise(0)
    )
    
    # Join with seller characteristics
    seller_features = monthly_seller_features.alias("msf").join(
        seller_performance.select(
            "seller_key", "seller_state", "seller_tier", "business_maturity"
        ).alias("sp"),
        col("msf.seller_key") == col("sp.seller_key"),
        "left"
    ).select(
        col("msf.*"),
        col("sp.seller_state"),
        col("sp.seller_tier"),
        col("sp.business_maturity")
    )
    
    # Add seasonal features
    seller_features = seller_features.withColumn(
        "quarter", quarter(to_date(concat(col("year_month"), lit("-01"))))
    ).withColumn(
        "is_holiday_month",
        when(col("month").isin(11, 12), 1).otherwise(0)
    )
    
    # Add performance prediction target (next month performance)
    seller_features = seller_features.withColumn(
        "next_month_orders", lead("monthly_orders", 1).over(window_spec)
    ).withColumn(
        "next_month_revenue", lead("monthly_revenue", 1).over(window_spec)
    )
    
    # Add metadata
    seller_features = seller_features.withColumn(
        "_gold_created_timestamp", current_timestamp()
    ).withColumn(
        "_gold_table_type", lit("ml_features_seller")
    )
    
    # Write to gold table
    seller_features.write \
        .format("delta") \
        .mode("overwrite") \
        .option("mergeSchema", "true") \
        .saveAsTable(f"{gold_schema}.ml_seller_performance_features")
    
    record_count = seller_features.count()
    logger.info(f"✅ Created seller performance features with {record_count} records")
    return record_count

# Execute seller performance features
seller_features_count = create_seller_performance_features()

# COMMAND ----------

print("🎉 Gold layer ML feature store completed successfully!")
print(f"🤖 Created ML feature tables:")
print(f"   - ml_customer_churn_features: {churn_features_count:,} records")
print(f"   - ml_demand_forecasting_features: {demand_features_count:,} records")
print(f"   - ml_recommendation_features: {recommendation_features_count:,} records")
print(f"   - ml_seller_performance_features: {seller_features_count:,} records")
print(f"\n🔬 Ready for ML use cases:")
print("   1. Customer churn prediction models")
print("   2. Product demand forecasting")
print("   3. Recommendation systems")
print("   4. Seller performance optimization")
print("   5. Dynamic pricing models")
