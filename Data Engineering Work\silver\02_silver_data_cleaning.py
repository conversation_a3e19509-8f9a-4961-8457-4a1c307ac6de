# Databricks notebook source
# MAGIC %md
# MAGIC # Silver Layer - Data Cleaning and Curation
# MAGIC 
# MAGIC This notebook transforms bronze layer data into clean, curated silver layer tables.
# MAGIC 
# MAGIC **Silver Layer Principles:**
# MAGIC - Clean and validate data quality
# MAGIC - Apply business rules and standardization
# MAGIC - Create conformed dimensions and facts
# MAGIC - Implement slowly changing dimensions (SCD)
# MAGIC - Add business-friendly column names and data types

# COMMAND ----------

# MAGIC %md
# MAGIC ## 1. Setup and Configuration

# COMMAND ----------

from pyspark.sql import SparkSession
from pyspark.sql.functions import *
from pyspark.sql.types import *
from delta.tables import DeltaTable
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get configuration
catalog_name = spark.conf.get("ecommerce.config.catalog_name", "ecommerce")
bronze_schema = spark.conf.get("ecommerce.config.bronze_schema", "ecommerce.bronze")
silver_schema = spark.conf.get("ecommerce.config.silver_schema", "ecommerce.silver")

print(f"🔧 Using catalog: {catalog_name}")
print(f"🥉 Reading from bronze: {bronze_schema}")
print(f"🥈 Writing to silver: {silver_schema}")

# Use the ecommerce catalog
spark.sql(f"USE CATALOG {catalog_name}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## 2. Silver Layer Helper Functions

# COMMAND ----------

def add_silver_metadata(df):
    """Add standard silver layer metadata columns"""
    return df.withColumn("_silver_processed_timestamp", current_timestamp()) \
             .withColumn("_silver_processed_date", current_date()) \
             .withColumn("_silver_data_quality_score", lit(1.0)) \
             .withColumn("_silver_record_hash", sha2(concat_ws("|", *df.columns), 256))

def clean_string_columns(df, columns):
    """Clean string columns by trimming whitespace and handling nulls"""
    for col_name in columns:
        if col_name in df.columns:
            df = df.withColumn(col_name, 
                              when(trim(col(col_name)) == "", None)
                              .otherwise(trim(col(col_name))))
    return df

def standardize_timestamps(df, timestamp_columns):
    """Standardize timestamp columns to consistent format"""
    for col_name in timestamp_columns:
        if col_name in df.columns:
            df = df.withColumn(col_name, 
                              to_timestamp(col(col_name), "yyyy-MM-dd HH:mm:ss"))
    return df

# COMMAND ----------

# MAGIC %md
# MAGIC ## 3. Clean Customers Dimension

# COMMAND ----------

def create_silver_customers():
    """Create cleaned customers dimension table"""
    
    # Read from bronze
    bronze_customers = spark.table(f"{bronze_schema}.customers")
    
    # Data cleaning and transformation
    silver_customers = bronze_customers.select(
        col("customer_id").alias("customer_key"),
        col("customer_unique_id").alias("customer_unique_key"),
        col("customer_zip_code_prefix").cast("string").alias("zip_code_prefix"),
        initcap(col("customer_city")).alias("city"),
        upper(col("customer_state")).alias("state_code")
    ).filter(
        col("customer_id").isNotNull()
    )
    
    # Add data quality checks
    silver_customers = silver_customers.withColumn(
        "_silver_data_quality_score",
        when(col("customer_key").isNull(), 0.0)
        .when(col("zip_code_prefix").isNull(), 0.7)
        .when(col("city").isNull(), 0.8)
        .when(col("state_code").isNull(), 0.8)
        .otherwise(1.0)
    )
    
    # Add silver metadata
    silver_customers = add_silver_metadata(silver_customers)
    
    # Write to silver table
    silver_customers.write \
        .format("delta") \
        .mode("overwrite") \
        .option("mergeSchema", "true") \
        .saveAsTable(f"{silver_schema}.dim_customers")
    
    record_count = silver_customers.count()
    logger.info(f"✅ Created silver customers dimension with {record_count} records")
    return record_count

# Execute customers transformation
customers_count = create_silver_customers()

# COMMAND ----------

# MAGIC %md
# MAGIC ## 4. Clean Products Dimension

# COMMAND ----------

def create_silver_products():
    """Create cleaned products dimension table"""
    
    # Read from bronze
    bronze_products = spark.table(f"{bronze_schema}.products")
    bronze_categories = spark.table(f"{bronze_schema}.product_category_translation")
    
    # Join products with category translations
    silver_products = bronze_products.alias("p").join(
        bronze_categories.alias("c"),
        col("p.product_category_name") == col("c.product_category_name"),
        "left"
    ).select(
        col("p.product_id").alias("product_key"),
        col("p.product_category_name").alias("category_name_portuguese"),
        coalesce(col("c.product_category_name_english"), 
                col("p.product_category_name")).alias("category_name_english"),
        col("p.product_name_lenght").alias("product_name_length"),
        col("p.product_description_lenght").alias("product_description_length"),
        col("p.product_photos_qty").alias("product_photos_quantity"),
        col("p.product_weight_g").alias("product_weight_grams"),
        col("p.product_length_cm").alias("product_length_cm"),
        col("p.product_height_cm").alias("product_height_cm"),
        col("p.product_width_cm").alias("product_width_cm")
    ).filter(
        col("product_key").isNotNull()
    )
    
    # Calculate product volume and add derived metrics
    silver_products = silver_products.withColumn(
        "product_volume_cm3",
        col("product_length_cm") * col("product_height_cm") * col("product_width_cm")
    ).withColumn(
        "product_size_category",
        when(col("product_volume_cm3") < 1000, "Small")
        .when(col("product_volume_cm3") < 10000, "Medium")
        .when(col("product_volume_cm3") < 100000, "Large")
        .otherwise("Extra Large")
    ).withColumn(
        "product_weight_category",
        when(col("product_weight_grams") < 100, "Light")
        .when(col("product_weight_grams") < 1000, "Medium")
        .when(col("product_weight_grams") < 5000, "Heavy")
        .otherwise("Extra Heavy")
    )
    
    # Add data quality score
    silver_products = silver_products.withColumn(
        "_silver_data_quality_score",
        when(col("product_key").isNull(), 0.0)
        .when(col("category_name_english").isNull(), 0.8)
        .when(col("product_weight_grams").isNull(), 0.9)
        .otherwise(1.0)
    )
    
    # Add silver metadata
    silver_products = add_silver_metadata(silver_products)
    
    # Write to silver table
    silver_products.write \
        .format("delta") \
        .mode("overwrite") \
        .option("mergeSchema", "true") \
        .saveAsTable(f"{silver_schema}.dim_products")
    
    record_count = silver_products.count()
    logger.info(f"✅ Created silver products dimension with {record_count} records")
    return record_count

# Execute products transformation
products_count = create_silver_products()

# COMMAND ----------

# MAGIC %md
# MAGIC ## 5. Clean Sellers Dimension

# COMMAND ----------

def create_silver_sellers():
    """Create cleaned sellers dimension table"""
    
    # Read from bronze
    bronze_sellers = spark.table(f"{bronze_schema}.sellers")
    
    # Data cleaning and transformation
    silver_sellers = bronze_sellers.select(
        col("seller_id").alias("seller_key"),
        col("seller_zip_code_prefix").cast("string").alias("seller_zip_code_prefix"),
        initcap(col("seller_city")).alias("seller_city"),
        upper(col("seller_state")).alias("seller_state_code")
    ).filter(
        col("seller_key").isNotNull()
    )
    
    # Add seller region classification
    silver_sellers = silver_sellers.withColumn(
        "seller_region",
        when(col("seller_state_code").isin("SP", "RJ", "MG", "ES"), "Southeast")
        .when(col("seller_state_code").isin("RS", "SC", "PR"), "South")
        .when(col("seller_state_code").isin("GO", "MT", "MS", "DF"), "Central-West")
        .when(col("seller_state_code").isin("BA", "SE", "PE", "AL", "PB", "RN", "CE", "PI", "MA"), "Northeast")
        .when(col("seller_state_code").isin("AM", "RR", "AP", "PA", "TO", "RO", "AC"), "North")
        .otherwise("Unknown")
    )
    
    # Add data quality score
    silver_sellers = silver_sellers.withColumn(
        "_silver_data_quality_score",
        when(col("seller_key").isNull(), 0.0)
        .when(col("seller_city").isNull(), 0.8)
        .when(col("seller_state_code").isNull(), 0.7)
        .otherwise(1.0)
    )
    
    # Add silver metadata
    silver_sellers = add_silver_metadata(silver_sellers)
    
    # Write to silver table
    silver_sellers.write \
        .format("delta") \
        .mode("overwrite") \
        .option("mergeSchema", "true") \
        .saveAsTable(f"{silver_schema}.dim_sellers")
    
    record_count = silver_sellers.count()
    logger.info(f"✅ Created silver sellers dimension with {record_count} records")
    return record_count

# Execute sellers transformation
sellers_count = create_silver_sellers()

# COMMAND ----------

# MAGIC %md
# MAGIC ## 6. Clean Orders Fact Table

# COMMAND ----------

def create_silver_orders():
    """Create cleaned orders fact table"""
    
    # Read from bronze
    bronze_orders = spark.table(f"{bronze_schema}.orders")
    
    # Standardize timestamp columns
    timestamp_columns = [
        "order_purchase_timestamp",
        "order_approved_at", 
        "order_delivered_carrier_date",
        "order_delivered_customer_date",
        "order_estimated_delivery_date"
    ]
    
    silver_orders = standardize_timestamps(bronze_orders, timestamp_columns)
    
    # Clean and transform
    silver_orders = silver_orders.select(
        col("order_id").alias("order_key"),
        col("customer_id").alias("customer_key"),
        col("order_status").alias("order_status"),
        col("order_purchase_timestamp").alias("order_purchase_datetime"),
        col("order_approved_at").alias("order_approved_datetime"),
        col("order_delivered_carrier_date").alias("order_shipped_datetime"),
        col("order_delivered_customer_date").alias("order_delivered_datetime"),
        col("order_estimated_delivery_date").alias("order_estimated_delivery_datetime")
    ).filter(
        col("order_key").isNotNull() & col("customer_key").isNotNull()
    )
    
    # Add derived date columns for analytics
    silver_orders = silver_orders.withColumn(
        "order_purchase_date", date(col("order_purchase_datetime"))
    ).withColumn(
        "order_year", year(col("order_purchase_datetime"))
    ).withColumn(
        "order_month", month(col("order_purchase_datetime"))
    ).withColumn(
        "order_quarter", quarter(col("order_purchase_datetime"))
    ).withColumn(
        "order_day_of_week", dayofweek(col("order_purchase_datetime"))
    )
    
    # Calculate delivery performance metrics
    silver_orders = silver_orders.withColumn(
        "days_to_ship",
        when(col("order_shipped_datetime").isNotNull() & col("order_approved_datetime").isNotNull(),
             datediff(col("order_shipped_datetime"), col("order_approved_datetime")))
    ).withColumn(
        "days_to_deliver",
        when(col("order_delivered_datetime").isNotNull() & col("order_shipped_datetime").isNotNull(),
             datediff(col("order_delivered_datetime"), col("order_shipped_datetime")))
    ).withColumn(
        "total_delivery_days",
        when(col("order_delivered_datetime").isNotNull() & col("order_purchase_datetime").isNotNull(),
             datediff(col("order_delivered_datetime"), col("order_purchase_datetime")))
    ).withColumn(
        "delivery_vs_estimate_days",
        when(col("order_delivered_datetime").isNotNull() & col("order_estimated_delivery_datetime").isNotNull(),
             datediff(col("order_delivered_datetime"), col("order_estimated_delivery_datetime")))
    )
    
    # Add delivery performance categories
    silver_orders = silver_orders.withColumn(
        "delivery_performance",
        when(col("delivery_vs_estimate_days") < -2, "Early")
        .when(col("delivery_vs_estimate_days") <= 2, "On Time")
        .when(col("delivery_vs_estimate_days") <= 7, "Slightly Late")
        .when(col("delivery_vs_estimate_days") > 7, "Very Late")
        .otherwise("Unknown")
    )
    
    # Add data quality score
    silver_orders = silver_orders.withColumn(
        "_silver_data_quality_score",
        when(col("order_key").isNull() | col("customer_key").isNull(), 0.0)
        .when(col("order_purchase_datetime").isNull(), 0.5)
        .when(col("order_status").isNull(), 0.8)
        .otherwise(1.0)
    )
    
    # Add silver metadata
    silver_orders = add_silver_metadata(silver_orders)
    
    # Write to silver table
    silver_orders.write \
        .format("delta") \
        .mode("overwrite") \
        .option("mergeSchema", "true") \
        .saveAsTable(f"{silver_schema}.fact_orders")
    
    record_count = silver_orders.count()
    logger.info(f"✅ Created silver orders fact table with {record_count} records")
    return record_count

# Execute orders transformation
orders_count = create_silver_orders()

# COMMAND ----------

print("🎉 Silver layer data cleaning completed successfully!")
print(f"📊 Processed tables:")
print(f"   - dim_customers: {customers_count:,} records")
print(f"   - dim_products: {products_count:,} records") 
print(f"   - dim_sellers: {sellers_count:,} records")
print(f"   - fact_orders: {orders_count:,} records")
print(f"\n📋 Next steps:")
print("   1. Create additional silver business tables")
print("   2. Implement data quality monitoring")
print("   3. Build gold layer aggregated tables")
