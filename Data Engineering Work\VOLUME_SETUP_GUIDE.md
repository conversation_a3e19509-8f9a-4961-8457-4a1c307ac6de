# Volume Setup Guide for Brazilian E-commerce Pipeline

## Overview

This guide provides step-by-step instructions for setting up and using the Brazilian e-commerce data pipeline with your existing volume configuration.

## Your Current Setup

### Volume Configuration
- **Catalog**: `ecommerce`
- **Schema**: `ecom_data`
- **Volume**: `ecom_data_volume`
- **Source Data Path**: `/Volumes/ecommerce/ecom_data/ecom_data_volume/data/`
- **Checkpoint Path**: `/Volumes/ecommerce/ecom_data/ecom_data_volume/checkPoint/`

### Expected Data Files
The following CSV files should be present in your data volume:
```
/Volumes/ecommerce/ecom_data/ecom_data_volume/data/
├── olist_customers_dataset.csv
├── olist_orders_dataset.csv
├── olist_order_items_dataset.csv
├── olist_products_dataset.csv
├── olist_sellers_dataset.csv
├── olist_order_payments_dataset.csv
├── olist_order_reviews_dataset.csv
├── olist_geolocation_dataset.csv
└── product_category_name_translation.csv
```

## Quick Start Instructions

### Step 1: Validate Your Setup
Run the volume validation notebook first to ensure everything is configured correctly:

```python
%run ./00_volume_setup_validation
```

**What this does:**
- ✅ Validates volume paths are accessible
- ✅ Checks all required data files are present
- ✅ Inspects sample data for quality
- ✅ Creates configuration table
- ✅ Sets up Spark configuration

### Step 2: Initialize Schemas
Create the medallion architecture schemas:

```python
%run ./00_setup_catalog_schemas
```

**What this creates:**
- `ecommerce.ecom_data` - Your data schema (already exists)
- `ecommerce.bronze` - Raw data layer
- `ecommerce.silver` - Curated data layer  
- `ecommerce.gold` - Analytics-ready layer

### Step 3: Ingest Bronze Layer
Load raw data from your volume into bronze tables:

```python
%run ./bronze/01_bronze_data_ingestion
```

**What this creates:**
- 9 bronze tables with raw data
- Metadata columns for lineage tracking
- Data quality validation

### Step 4: Process Silver Layer
Clean and curate the data:

```python
%run ./silver/02_silver_data_cleaning
%run ./silver/03_silver_business_tables
```

**What this creates:**
- Dimension tables (customers, products, sellers)
- Fact tables (orders, order items)
- Business analytics tables

### Step 5: Create Gold Layer
Generate analytics and ML-ready tables:

```python
%run ./gold/04_gold_analytics_tables
%run ./gold/05_gold_ml_feature_store
```

**What this creates:**
- Executive dashboard tables
- Customer segmentation
- ML feature store
- Performance analytics

### Step 6: Set Up Monitoring
Configure pipeline orchestration and monitoring:

```python
%run ./06_pipeline_orchestration
```

**What this creates:**
- Data quality framework
- Pipeline monitoring
- Automated workflows

## Troubleshooting

### Common Issues and Solutions

#### 1. Volume Path Not Found
**Error**: `Path does not exist: /Volumes/ecommerce/ecom_data/ecom_data_volume/data/`

**Solution**:
```sql
-- Check if volume exists
SHOW VOLUMES IN ecommerce.ecom_data;

-- If volume doesn't exist, create it
CREATE VOLUME IF NOT EXISTS ecommerce.ecom_data.ecom_data_volume;
```

#### 2. Data Files Missing
**Error**: CSV files not found in volume

**Solution**:
```python
# Upload files to the volume using Databricks UI or CLI
# Or copy from another location
dbutils.fs.cp("source_path/", "/Volumes/ecommerce/ecom_data/ecom_data_volume/data/", recurse=True)
```

#### 3. Schema Permission Issues
**Error**: Permission denied when creating schemas

**Solution**:
```sql
-- Grant necessary permissions
GRANT CREATE SCHEMA ON CATALOG ecommerce TO `your_user_or_group`;
GRANT USE CATALOG ON CATALOG ecommerce TO `your_user_or_group`;
```

#### 4. Table Creation Failures
**Error**: Cannot create tables in schema

**Solution**:
```sql
-- Grant table creation permissions
GRANT CREATE TABLE ON SCHEMA ecommerce.bronze TO `your_user_or_group`;
GRANT CREATE TABLE ON SCHEMA ecommerce.silver TO `your_user_or_group`;
GRANT CREATE TABLE ON SCHEMA ecommerce.gold TO `your_user_or_group`;
```

## Data Validation Checklist

Before running the pipeline, ensure:

- [ ] All 9 CSV files are present in the data volume
- [ ] Files are readable and not corrupted
- [ ] Volume paths are accessible from Databricks
- [ ] Appropriate permissions are granted
- [ ] Checkpoint directory is writable

## Volume Management Best Practices

### 1. Data Organization
```
/Volumes/ecommerce/ecom_data/ecom_data_volume/
├── data/                    # Source CSV files
├── checkPoint/              # Streaming checkpoints
├── archive/                 # Historical data backups
└── temp/                    # Temporary processing files
```

### 2. Access Control
```sql
-- Grant read access to data consumers
GRANT READ VOLUME ON VOLUME ecommerce.ecom_data.ecom_data_volume TO `data_analysts`;

-- Grant write access to data engineers
GRANT WRITE VOLUME ON VOLUME ecommerce.ecom_data.ecom_data_volume TO `data_engineers`;
```

### 3. Monitoring Volume Usage
```python
# Check volume size and usage
dbutils.fs.ls("/Volumes/ecommerce/ecom_data/ecom_data_volume/")

# Monitor file sizes
for file_info in dbutils.fs.ls("/Volumes/ecommerce/ecom_data/ecom_data_volume/data/"):
    print(f"{file_info.name}: {file_info.size / (1024*1024):.2f} MB")
```

## Performance Optimization

### 1. File Format Optimization
- Source files are CSV (acceptable for initial load)
- Bronze/Silver/Gold layers use Delta format for performance
- Automatic compression and optimization

### 2. Partitioning Strategy
- Bronze: Partitioned by ingestion date
- Silver: Partitioned by business keys (customer_state, order_year)
- Gold: Optimized for query patterns

### 3. Caching Strategy
```python
# Cache frequently accessed tables
spark.sql("CACHE TABLE ecommerce.silver.fact_orders")
spark.sql("CACHE TABLE ecommerce.gold.customer_segmentation")
```

## Next Steps

After successful setup:

1. **Schedule Regular Runs**: Set up Databricks Jobs for automated execution
2. **Monitor Data Quality**: Review quality reports and set up alerts
3. **Create Dashboards**: Connect Power BI or other BI tools to gold tables
4. **Implement ML Models**: Use feature store for model training
5. **Set Up Alerts**: Configure notifications for pipeline failures

## Support

If you encounter issues:

1. Check the validation notebook output for specific errors
2. Review Databricks cluster logs for detailed error messages
3. Verify Unity Catalog permissions and access rights
4. Ensure all required data files are present and accessible

## Configuration Reference

Your pipeline uses these key configurations:

```python
volume_config = {
    "catalog_name": "ecommerce",
    "data_schema": "ecommerce.ecom_data", 
    "volume_name": "ecom_data_volume",
    "source_data_path": "/Volumes/ecommerce/ecom_data/ecom_data_volume/data/",
    "checkpoint_path": "/Volumes/ecommerce/ecom_data/ecom_data_volume/checkPoint/",
    "bronze_schema": "ecommerce.bronze",
    "silver_schema": "ecommerce.silver",
    "gold_schema": "ecommerce.gold"
}
```

These configurations are automatically set in Spark conf and available across all notebooks in the pipeline.
