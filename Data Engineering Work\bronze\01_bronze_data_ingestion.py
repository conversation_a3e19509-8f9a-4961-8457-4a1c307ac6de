# Databricks notebook source
# MAGIC %md
# MAGIC # Bronze Layer - Raw Data Ingestion
# MAGIC 
# MAGIC This notebook ingests raw CSV files from the Brazilian e-commerce dataset into Delta Lake tables in the bronze layer.
# MAGIC 
# MAGIC **Bronze Layer Principles:**
# MAGIC - Store data as-is from source systems
# MAGIC - Minimal transformation (only add metadata columns)
# MAGIC - Preserve data lineage and audit trail
# MAGIC - Enable schema evolution and historical reprocessing

# COMMAND ----------

# MAGIC %md
# MAGIC ## 1. Setup and Configuration

# COMMAND ----------

from pyspark.sql import SparkSession
from pyspark.sql.functions import *
from pyspark.sql.types import *
from delta.tables import DeltaTable
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get configuration from previous setup
catalog_name = spark.conf.get("ecommerce.config.catalog_name", "ecommerce")
bronze_schema = spark.conf.get("ecommerce.config.bronze_schema", "ecommerce.bronze")
source_data_path = spark.conf.get("ecommerce.config.source_data_path", "/Volumes/ecommerce/ecom_data/ecom_data_volume/data/")

print(f"🔧 Using catalog: {catalog_name}")
print(f"🥉 Using bronze schema: {bronze_schema}")
print(f"📁 Using source data path: {source_data_path}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## 2. Data Source Configuration

# COMMAND ----------

# Define data sources and their configurations using volume paths
data_sources = {
    "customers": {
        "file_path": f"{source_data_path}olist_customers_dataset.csv",
        "table_name": f"{bronze_schema}.customers",
        "description": "Customer demographic and location information"
    },
    "orders": {
        "file_path": f"{source_data_path}olist_orders_dataset.csv",
        "table_name": f"{bronze_schema}.orders",
        "description": "Order lifecycle and status information"
    },
    "order_items": {
        "file_path": f"{source_data_path}olist_order_items_dataset.csv",
        "table_name": f"{bronze_schema}.order_items",
        "description": "Individual items within each order"
    },
    "products": {
        "file_path": f"{source_data_path}olist_products_dataset.csv",
        "table_name": f"{bronze_schema}.products",
        "description": "Product catalog with attributes and dimensions"
    },
    "sellers": {
        "file_path": f"{source_data_path}olist_sellers_dataset.csv",
        "table_name": f"{bronze_schema}.sellers",
        "description": "Seller information and geographic location"
    },
    "order_payments": {
        "file_path": f"{source_data_path}olist_order_payments_dataset.csv",
        "table_name": f"{bronze_schema}.order_payments",
        "description": "Payment information for orders"
    },
    "order_reviews": {
        "file_path": f"{source_data_path}olist_order_reviews_dataset.csv",
        "table_name": f"{bronze_schema}.order_reviews",
        "description": "Customer reviews and ratings"
    },
    "geolocation": {
        "file_path": f"{source_data_path}olist_geolocation_dataset.csv",
        "table_name": f"{bronze_schema}.geolocation",
        "description": "Geographic coordinates for Brazilian ZIP codes"
    },
    "product_category_translation": {
        "file_path": f"{source_data_path}product_category_name_translation.csv",
        "table_name": f"{bronze_schema}.product_category_translation",
        "description": "Product category translations from Portuguese to English"
    }
}

print(f"📊 Configured {len(data_sources)} data sources for ingestion")

# COMMAND ----------

# MAGIC %md
# MAGIC ## 3. Bronze Layer Ingestion Functions

# COMMAND ----------

def add_bronze_metadata(df):
    """
    Add standard bronze layer metadata columns to the dataframe
    """
    return df.withColumn("_bronze_ingestion_timestamp", current_timestamp()) \
             .withColumn("_bronze_ingestion_date", current_date()) \
             .withColumn("_bronze_source_file", lit("csv_upload")) \
             .withColumn("_bronze_record_id", monotonically_increasing_id())

def ingest_csv_to_bronze(source_config, overwrite=False):
    """
    Ingest CSV file to bronze Delta table with metadata
    """
    try:
        file_path = source_config["file_path"]
        table_name = source_config["table_name"]
        description = source_config["description"]
        
        logger.info(f"🔄 Starting ingestion for {table_name}")
        
        # Read CSV with header and infer schema
        df = spark.read.option("header", "true") \
                      .option("inferSchema", "true") \
                      .option("multiline", "true") \
                      .option("escape", '"') \
                      .csv(file_path)
        
        # Add bronze metadata
        df_with_metadata = add_bronze_metadata(df)
        
        # Write to Delta table
        write_mode = "overwrite" if overwrite else "append"
        
        df_with_metadata.write \
            .format("delta") \
            .mode(write_mode) \
            .option("mergeSchema", "true") \
            .saveAsTable(table_name)
        
        # Add table comment
        spark.sql(f"COMMENT ON TABLE {table_name} IS '{description}'")
        
        record_count = df.count()
        logger.info(f"✅ Successfully ingested {record_count} records to {table_name}")
        
        return {
            "table_name": table_name,
            "record_count": record_count,
            "status": "success",
            "ingestion_timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to ingest {table_name}: {str(e)}")
        return {
            "table_name": table_name,
            "record_count": 0,
            "status": "failed",
            "error": str(e),
            "ingestion_timestamp": datetime.now()
        }

# COMMAND ----------

# MAGIC %md
# MAGIC ## 4. Execute Bronze Layer Ingestion

# COMMAND ----------

# Use the ecommerce catalog
spark.sql(f"USE CATALOG {catalog_name}")

# Track ingestion results
ingestion_results = []

# Ingest each data source
for source_name, source_config in data_sources.items():
    print(f"\n📥 Ingesting {source_name}...")
    result = ingest_csv_to_bronze(source_config, overwrite=True)
    ingestion_results.append(result)
    
    if result["status"] == "success":
        print(f"   ✅ {result['record_count']} records ingested")
    else:
        print(f"   ❌ Ingestion failed: {result.get('error', 'Unknown error')}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## 5. Ingestion Summary and Validation

# COMMAND ----------

# Create summary DataFrame
summary_data = []
for result in ingestion_results:
    summary_data.append((
        result["table_name"],
        result["record_count"], 
        result["status"],
        result["ingestion_timestamp"]
    ))

summary_df = spark.createDataFrame(
    summary_data,
    ["table_name", "record_count", "status", "ingestion_timestamp"]
)

print("📊 BRONZE LAYER INGESTION SUMMARY")
print("=" * 50)
summary_df.show(truncate=False)

# Calculate totals
total_records = sum([r["record_count"] for r in ingestion_results if r["status"] == "success"])
successful_tables = len([r for r in ingestion_results if r["status"] == "success"])
failed_tables = len([r for r in ingestion_results if r["status"] == "failed"])

print(f"\n📈 INGESTION STATISTICS:")
print(f"   Total Records Ingested: {total_records:,}")
print(f"   Successful Tables: {successful_tables}")
print(f"   Failed Tables: {failed_tables}")
print(f"   Success Rate: {(successful_tables/len(ingestion_results)*100):.1f}%")

# COMMAND ----------

# MAGIC %md
# MAGIC ## 6. Data Quality Checks

# COMMAND ----------

# MAGIC %sql
# MAGIC -- Verify all bronze tables exist
# MAGIC SHOW TABLES IN ecommerce.bronze;

# COMMAND ----------

# MAGIC %sql
# MAGIC -- Check record counts for each table
# MAGIC SELECT 
#     'customers' as table_name,
#     COUNT(*) as record_count,
#     MIN(_bronze_ingestion_timestamp) as first_ingestion,
#     MAX(_bronze_ingestion_timestamp) as last_ingestion
# MAGIC FROM ecommerce.bronze.customers
# MAGIC 
# MAGIC UNION ALL
# MAGIC 
# MAGIC SELECT 
#     'orders' as table_name,
#     COUNT(*) as record_count,
#     MIN(_bronze_ingestion_timestamp) as first_ingestion,
#     MAX(_bronze_ingestion_timestamp) as last_ingestion
# MAGIC FROM ecommerce.bronze.orders
# MAGIC 
# MAGIC UNION ALL
# MAGIC 
# MAGIC SELECT 
#     'order_items' as table_name,
#     COUNT(*) as record_count,
#     MIN(_bronze_ingestion_timestamp) as first_ingestion,
#     MAX(_bronze_ingestion_timestamp) as last_ingestion
# MAGIC FROM ecommerce.bronze.order_items
# MAGIC 
# MAGIC ORDER BY table_name;

# COMMAND ----------

print("🎉 Bronze layer ingestion completed successfully!")
print("📋 Next steps:")
print("   1. Run silver layer data cleaning and curation")
print("   2. Implement data quality validations")
print("   3. Set up automated ingestion workflows")
print("   4. Monitor data freshness and completeness")
