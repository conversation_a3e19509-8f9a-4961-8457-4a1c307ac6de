# Databricks notebook source
# MAGIC %md
# MAGIC # Gold Layer - Analytics-Ready Tables for Data Analysis Team
# MAGIC 
# MAGIC This notebook creates gold layer tables optimized for business analytics, reporting, and dashboards.
# MAGIC These tables are pre-aggregated and optimized for fast query performance.

# COMMAND ----------

# MAGIC %md
# MAGIC ## 1. Setup and Configuration

# COMMAND ----------

from pyspark.sql import SparkSession
from pyspark.sql.functions import *
from pyspark.sql.types import *
from delta.tables import DeltaTable
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get configuration
catalog_name = spark.conf.get("ecommerce.config.catalog_name", "ecommerce")
silver_schema = spark.conf.get("ecommerce.config.silver_schema", "ecommerce.silver")
gold_schema = spark.conf.get("ecommerce.config.gold_schema", "ecommerce.gold")

# Use the ecommerce catalog
spark.sql(f"USE CATALOG {catalog_name}")

print(f"🔧 Creating analytics tables in: {gold_schema}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## 2. Daily Sales Summary for Executive Dashboards

# COMMAND ----------

def create_daily_sales_summary():
    """Create daily sales summary for executive reporting"""
    
    # Read from silver tables
    order_items = spark.table(f"{silver_schema}.fact_order_items")
    
    # Create daily aggregations
    daily_sales = order_items.filter(
        col("order_status") == "delivered"
    ).groupBy(
        date(col("order_purchase_datetime")).alias("sales_date"),
        col("order_year"),
        col("order_month"),
        col("order_quarter")
    ).agg(
        # Volume metrics
        count("order_key").alias("total_orders"),
        countDistinct("order_key").alias("unique_orders"),
        countDistinct("customer_key").alias("unique_customers"),
        countDistinct("seller_key").alias("unique_sellers"),
        countDistinct("product_key").alias("unique_products"),
        
        # Revenue metrics
        sum("item_price").alias("gross_revenue"),
        sum("freight_cost").alias("freight_revenue"),
        sum("total_item_value").alias("total_revenue"),
        avg("item_price").alias("avg_item_price"),
        avg("total_item_value").alias("avg_order_value"),
        
        # Product mix
        countDistinct("product_category").alias("categories_sold")
    )
    
    # Add derived metrics
    daily_sales = daily_sales.withColumn(
        "revenue_per_customer", col("total_revenue") / col("unique_customers")
    ).withColumn(
        "revenue_per_order", col("total_revenue") / col("unique_orders")
    ).withColumn(
        "items_per_order", col("total_orders") / col("unique_orders")
    ).withColumn(
        "day_of_week", dayofweek(col("sales_date"))
    ).withColumn(
        "day_name", 
        when(col("day_of_week") == 1, "Sunday")
        .when(col("day_of_week") == 2, "Monday")
        .when(col("day_of_week") == 3, "Tuesday")
        .when(col("day_of_week") == 4, "Wednesday")
        .when(col("day_of_week") == 5, "Thursday")
        .when(col("day_of_week") == 6, "Friday")
        .otherwise("Saturday")
    )
    
    # Add metadata
    daily_sales = daily_sales.withColumn(
        "_gold_created_timestamp", current_timestamp()
    ).withColumn(
        "_gold_table_type", lit("analytics_summary")
    )
    
    # Write to gold table
    daily_sales.write \
        .format("delta") \
        .mode("overwrite") \
        .option("mergeSchema", "true") \
        .saveAsTable(f"{gold_schema}.daily_sales_summary")
    
    record_count = daily_sales.count()
    logger.info(f"✅ Created daily sales summary with {record_count} records")
    return record_count

# Execute daily sales summary
daily_sales_count = create_daily_sales_summary()

# COMMAND ----------

# MAGIC %md
# MAGIC ## 3. Customer Segmentation Analysis

# COMMAND ----------

def create_customer_segmentation():
    """Create customer segmentation analysis for marketing team"""
    
    # Read from silver tables
    order_items = spark.table(f"{silver_schema}.fact_order_items")
    payment_behavior = spark.table(f"{silver_schema}.customer_payment_behavior")
    customers = spark.table(f"{silver_schema}.dim_customers")
    
    # Calculate RFM metrics (Recency, Frequency, Monetary)
    current_date = lit("2018-12-31")  # Use dataset end date as reference
    
    customer_rfm = order_items.filter(
        col("order_status") == "delivered"
    ).groupBy("customer_key").agg(
        # Recency: Days since last purchase
        datediff(current_date, max("order_purchase_datetime")).alias("recency_days"),
        
        # Frequency: Number of orders
        countDistinct("order_key").alias("frequency_orders"),
        
        # Monetary: Total spent
        sum("total_item_value").alias("monetary_value"),
        
        # Additional metrics
        avg("total_item_value").alias("avg_order_value"),
        countDistinct("product_category").alias("category_diversity"),
        min("order_purchase_datetime").alias("first_purchase_date"),
        max("order_purchase_datetime").alias("last_purchase_date")
    )
    
    # Calculate RFM scores using percentiles
    rfm_percentiles = customer_rfm.select(
        expr("percentile_approx(recency_days, 0.2)").alias("recency_20"),
        expr("percentile_approx(recency_days, 0.4)").alias("recency_40"),
        expr("percentile_approx(recency_days, 0.6)").alias("recency_60"),
        expr("percentile_approx(recency_days, 0.8)").alias("recency_80"),
        expr("percentile_approx(frequency_orders, 0.2)").alias("frequency_20"),
        expr("percentile_approx(frequency_orders, 0.4)").alias("frequency_40"),
        expr("percentile_approx(frequency_orders, 0.6)").alias("frequency_60"),
        expr("percentile_approx(frequency_orders, 0.8)").alias("frequency_80"),
        expr("percentile_approx(monetary_value, 0.2)").alias("monetary_20"),
        expr("percentile_approx(monetary_value, 0.4)").alias("monetary_40"),
        expr("percentile_approx(monetary_value, 0.6)").alias("monetary_60"),
        expr("percentile_approx(monetary_value, 0.8)").alias("monetary_80")
    ).collect()[0]
    
    # Add RFM scores
    customer_segmentation = customer_rfm.withColumn(
        "recency_score",
        when(col("recency_days") <= rfm_percentiles["recency_20"], 5)
        .when(col("recency_days") <= rfm_percentiles["recency_40"], 4)
        .when(col("recency_days") <= rfm_percentiles["recency_60"], 3)
        .when(col("recency_days") <= rfm_percentiles["recency_80"], 2)
        .otherwise(1)
    ).withColumn(
        "frequency_score",
        when(col("frequency_orders") >= rfm_percentiles["frequency_80"], 5)
        .when(col("frequency_orders") >= rfm_percentiles["frequency_60"], 4)
        .when(col("frequency_orders") >= rfm_percentiles["frequency_40"], 3)
        .when(col("frequency_orders") >= rfm_percentiles["frequency_20"], 2)
        .otherwise(1)
    ).withColumn(
        "monetary_score",
        when(col("monetary_value") >= rfm_percentiles["monetary_80"], 5)
        .when(col("monetary_value") >= rfm_percentiles["monetary_60"], 4)
        .when(col("monetary_value") >= rfm_percentiles["monetary_40"], 3)
        .when(col("monetary_value") >= rfm_percentiles["monetary_20"], 2)
        .otherwise(1)
    )
    
    # Create customer segments
    customer_segmentation = customer_segmentation.withColumn(
        "rfm_score", col("recency_score") + col("frequency_score") + col("monetary_score")
    ).withColumn(
        "customer_segment",
        when((col("recency_score") >= 4) & (col("frequency_score") >= 4) & (col("monetary_score") >= 4), "Champions")
        .when((col("recency_score") >= 3) & (col("frequency_score") >= 3) & (col("monetary_score") >= 4), "Loyal Customers")
        .when((col("recency_score") >= 4) & (col("frequency_score") <= 2), "New Customers")
        .when((col("recency_score") >= 3) & (col("frequency_score") >= 3) & (col("monetary_score") <= 3), "Potential Loyalists")
        .when((col("recency_score") >= 3) & (col("frequency_score") <= 2) & (col("monetary_score") >= 3), "Big Spenders")
        .when((col("recency_score") <= 2) & (col("frequency_score") >= 3) & (col("monetary_score") >= 3), "At Risk")
        .when((col("recency_score") <= 2) & (col("frequency_score") <= 2) & (col("monetary_score") >= 4), "Can't Lose Them")
        .when((col("recency_score") <= 2) & (col("frequency_score") <= 2) & (col("monetary_score") <= 2), "Lost Customers")
        .otherwise("Others")
    )
    
    # Join with customer demographics and payment behavior
    customer_segmentation = customer_segmentation.alias("cs") \
        .join(customers.alias("c"), col("cs.customer_key") == col("c.customer_key"), "left") \
        .join(payment_behavior.alias("pb"), col("cs.customer_key") == col("pb.customer_key"), "left") \
        .select(
            col("cs.*"),
            col("c.state_code").alias("customer_state"),
            col("c.city").alias("customer_city"),
            col("pb.preferred_payment_method"),
            col("pb.customer_value_segment"),
            col("pb.installment_preference")
        )
    
    # Add customer lifetime metrics
    customer_segmentation = customer_segmentation.withColumn(
        "customer_lifetime_days",
        datediff(col("last_purchase_date"), col("first_purchase_date"))
    ).withColumn(
        "purchase_frequency_days",
        col("customer_lifetime_days") / greatest(col("frequency_orders") - 1, lit(1))
    ).withColumn(
        "customer_lifetime_value_score",
        when(col("monetary_value") >= 1000, "High")
        .when(col("monetary_value") >= 300, "Medium")
        .otherwise("Low")
    )
    
    # Add metadata
    customer_segmentation = customer_segmentation.withColumn(
        "_gold_created_timestamp", current_timestamp()
    ).withColumn(
        "_gold_table_type", lit("customer_analytics")
    )
    
    # Write to gold table
    customer_segmentation.write \
        .format("delta") \
        .mode("overwrite") \
        .option("mergeSchema", "true") \
        .saveAsTable(f"{gold_schema}.customer_segmentation")
    
    record_count = customer_segmentation.count()
    logger.info(f"✅ Created customer segmentation with {record_count} records")
    return record_count

# Execute customer segmentation
customer_seg_count = create_customer_segmentation()

# COMMAND ----------

# MAGIC %md
# MAGIC ## 4. Product Category Performance Dashboard

# COMMAND ----------

def create_category_performance():
    """Create product category performance analysis"""
    
    # Read from silver tables
    order_items = spark.table(f"{silver_schema}.fact_order_items")
    product_performance = spark.table(f"{silver_schema}.product_performance")
    
    # Aggregate by category and time period
    category_performance = order_items.filter(
        col("order_status") == "delivered"
    ).groupBy(
        col("product_category"),
        col("order_year"),
        col("order_quarter")
    ).agg(
        # Volume metrics
        count("order_key").alias("total_orders"),
        countDistinct("order_key").alias("unique_orders"),
        countDistinct("customer_key").alias("unique_customers"),
        countDistinct("product_key").alias("unique_products"),
        countDistinct("seller_key").alias("unique_sellers"),
        
        # Revenue metrics
        sum("item_price").alias("gross_revenue"),
        sum("freight_cost").alias("freight_revenue"),
        sum("total_item_value").alias("total_revenue"),
        avg("item_price").alias("avg_item_price"),
        avg("total_item_value").alias("avg_order_value"),
        
        # Performance metrics
        avg("product_weight_grams").alias("avg_product_weight")
    )
    
    # Add derived metrics
    category_performance = category_performance.withColumn(
        "revenue_per_customer", col("total_revenue") / col("unique_customers")
    ).withColumn(
        "revenue_per_product", col("total_revenue") / col("unique_products")
    ).withColumn(
        "market_penetration", col("unique_customers") / lit(100000.0)  # Normalize by total customers
    )
    
    # Add category rankings within each quarter
    window_spec = Window.partitionBy("order_year", "order_quarter").orderBy(desc("total_revenue"))
    
    category_performance = category_performance.withColumn(
        "revenue_rank", row_number().over(window_spec)
    ).withColumn(
        "revenue_percentile", 
        percent_rank().over(window_spec)
    )
    
    # Add performance tiers
    category_performance = category_performance.withColumn(
        "performance_tier",
        when(col("revenue_rank") <= 3, "Top Tier")
        .when(col("revenue_rank") <= 10, "High Tier")
        .when(col("revenue_percentile") >= 0.5, "Medium Tier")
        .otherwise("Low Tier")
    )
    
    # Add metadata
    category_performance = category_performance.withColumn(
        "_gold_created_timestamp", current_timestamp()
    ).withColumn(
        "_gold_table_type", lit("category_analytics")
    )
    
    # Write to gold table
    category_performance.write \
        .format("delta") \
        .mode("overwrite") \
        .option("mergeSchema", "true") \
        .saveAsTable(f"{gold_schema}.category_performance")
    
    record_count = category_performance.count()
    logger.info(f"✅ Created category performance with {record_count} records")
    return record_count

# Execute category performance
category_perf_count = create_category_performance()

# COMMAND ----------

# MAGIC %md
# MAGIC ## 5. Geographic Sales Analysis

# COMMAND ----------

def create_geographic_analysis():
    """Create geographic sales analysis for market expansion"""
    
    # Read from silver tables
    order_items = spark.table(f"{silver_schema}.fact_order_items")
    customers = spark.table(f"{silver_schema}.dim_customers")
    sellers = spark.table(f"{silver_schema}.dim_sellers")
    
    # Join with geographic information
    geographic_sales = order_items.alias("oi") \
        .join(customers.alias("c"), col("oi.customer_key") == col("c.customer_key"), "left") \
        .join(sellers.alias("s"), col("oi.seller_key") == col("s.seller_key"), "left") \
        .filter(col("oi.order_status") == "delivered")
    
    # Aggregate by customer state
    state_performance = geographic_sales.groupBy(
        col("c.state_code").alias("customer_state"),
        col("oi.order_year")
    ).agg(
        # Volume metrics
        count("oi.order_key").alias("total_orders"),
        countDistinct("oi.order_key").alias("unique_orders"),
        countDistinct("oi.customer_key").alias("unique_customers"),
        countDistinct("oi.seller_key").alias("unique_sellers"),
        countDistinct("oi.product_key").alias("unique_products"),
        
        # Revenue metrics
        sum("oi.total_item_value").alias("total_revenue"),
        avg("oi.total_item_value").alias("avg_order_value"),
        
        # Geographic diversity
        countDistinct("s.seller_state_code").alias("seller_states_count")
    )
    
    # Add market metrics
    state_performance = state_performance.withColumn(
        "revenue_per_customer", col("total_revenue") / col("unique_customers")
    ).withColumn(
        "orders_per_customer", col("unique_orders") / col("unique_customers")
    ).withColumn(
        "seller_diversity_score", col("seller_states_count") / lit(27.0)  # Brazil has 27 states
    )
    
    # Add state rankings
    window_spec = Window.partitionBy("order_year").orderBy(desc("total_revenue"))
    
    state_performance = state_performance.withColumn(
        "revenue_rank", row_number().over(window_spec)
    ).withColumn(
        "market_share", 
        col("total_revenue") / sum("total_revenue").over(Window.partitionBy("order_year"))
    )
    
    # Classify market maturity
    state_performance = state_performance.withColumn(
        "market_maturity",
        when(col("revenue_rank") <= 5, "Mature Market")
        .when(col("revenue_rank") <= 15, "Growing Market")
        .when(col("unique_customers") >= 1000, "Emerging Market")
        .otherwise("New Market")
    )
    
    # Add metadata
    state_performance = state_performance.withColumn(
        "_gold_created_timestamp", current_timestamp()
    ).withColumn(
        "_gold_table_type", lit("geographic_analytics")
    )
    
    # Write to gold table
    state_performance.write \
        .format("delta") \
        .mode("overwrite") \
        .option("mergeSchema", "true") \
        .saveAsTable(f"{gold_schema}.geographic_performance")
    
    record_count = state_performance.count()
    logger.info(f"✅ Created geographic performance with {record_count} records")
    return record_count

# Execute geographic analysis
geo_analysis_count = create_geographic_analysis()

# COMMAND ----------

print("🎉 Gold layer analytics tables completed successfully!")
print(f"📊 Created analytics tables:")
print(f"   - daily_sales_summary: {daily_sales_count:,} records")
print(f"   - customer_segmentation: {customer_seg_count:,} records")
print(f"   - category_performance: {category_perf_count:,} records")
print(f"   - geographic_performance: {geo_analysis_count:,} records")
print(f"\n📋 Ready for:")
print("   1. Executive dashboards and reporting")
print("   2. Marketing campaign optimization")
print("   3. Market expansion analysis")
print("   4. Performance monitoring")
