# Databricks notebook source
# MAGIC %md
# MAGIC # Volume Setup and Data Validation
# MAGIC 
# MAGIC This notebook validates the volume setup and data availability for the Brazilian e-commerce pipeline.
# MAGIC It checks the volume paths, data files, and prepares the configuration for the medallion architecture.

# COMMAND ----------

# MAGIC %md
# MAGIC ## 1. Setup and Configuration

# COMMAND ----------

from pyspark.sql import SparkSession
from pyspark.sql.functions import *
from pyspark.sql.types import *
import logging
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Volume configuration
volume_config = {
    "catalog_name": "ecommerce",
    "data_schema": "ecommerce.ecom_data",
    "volume_name": "ecom_data_volume",
    "source_data_path": "/Volumes/ecommerce/ecom_data/ecom_data_volume/data/",
    "checkpoint_path": "/Volumes/ecommerce/ecom_data/ecom_data_volume/checkPoint/",
    "bronze_schema": "ecommerce.bronze",
    "silver_schema": "ecommerce.silver",
    "gold_schema": "ecommerce.gold"
}

print("🔧 Volume Configuration:")
for key, value in volume_config.items():
    print(f"   {key}: {value}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## 2. Volume and Path Validation

# COMMAND ----------

def validate_volume_path(path, description):
    """Validate that a volume path exists and is accessible"""
    try:
        # List contents of the path
        files = dbutils.fs.ls(path)
        print(f"✅ {description} - Path exists: {path}")
        print(f"   Found {len(files)} items")
        return True, files
    except Exception as e:
        print(f"❌ {description} - Path not accessible: {path}")
        print(f"   Error: {str(e)}")
        return False, []

# Validate volume paths
print("🔍 Validating Volume Paths:")
print("=" * 50)

# Check source data path
data_exists, data_files = validate_volume_path(
    volume_config["source_data_path"], 
    "Source Data Volume"
)

# Check checkpoint path
checkpoint_exists, checkpoint_files = validate_volume_path(
    volume_config["checkpoint_path"], 
    "Checkpoint Volume"
)

# COMMAND ----------

# MAGIC %md
# MAGIC ## 3. Data Files Validation

# COMMAND ----------

# Expected data files
expected_files = [
    "olist_customers_dataset.csv",
    "olist_orders_dataset.csv",
    "olist_order_items_dataset.csv",
    "olist_products_dataset.csv",
    "olist_sellers_dataset.csv",
    "olist_order_payments_dataset.csv",
    "olist_order_reviews_dataset.csv",
    "olist_geolocation_dataset.csv",
    "product_category_name_translation.csv"
]

def validate_data_files():
    """Validate that all expected data files are present"""
    print("📁 Validating Data Files:")
    print("=" * 50)
    
    if not data_exists:
        print("❌ Cannot validate files - source data path not accessible")
        return False
    
    # Get list of file names from the volume
    available_files = [file.name for file in data_files if file.name.endswith('.csv')]
    
    missing_files = []
    present_files = []
    
    for expected_file in expected_files:
        if expected_file in available_files:
            present_files.append(expected_file)
            print(f"   ✅ {expected_file}")
        else:
            missing_files.append(expected_file)
            print(f"   ❌ {expected_file} - MISSING")
    
    print(f"\n📊 File Validation Summary:")
    print(f"   Present: {len(present_files)}/{len(expected_files)}")
    print(f"   Missing: {len(missing_files)}")
    
    if missing_files:
        print(f"\n⚠️  Missing Files:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    else:
        print(f"\n🎉 All expected files are present!")
        return True

# Validate data files
files_valid = validate_data_files()

# COMMAND ----------

# MAGIC %md
# MAGIC ## 4. Sample Data Inspection

# COMMAND ----------

def inspect_sample_data():
    """Inspect sample data from each file to validate structure"""
    print("🔍 Sample Data Inspection:")
    print("=" * 50)
    
    if not files_valid:
        print("❌ Cannot inspect data - files validation failed")
        return
    
    sample_files = [
        ("olist_customers_dataset.csv", "Customers"),
        ("olist_orders_dataset.csv", "Orders"),
        ("olist_order_items_dataset.csv", "Order Items"),
        ("olist_products_dataset.csv", "Products")
    ]
    
    for file_name, description in sample_files:
        try:
            file_path = volume_config["source_data_path"] + file_name
            
            # Read sample data
            df = spark.read.option("header", "true") \
                          .option("inferSchema", "true") \
                          .csv(file_path)
            
            record_count = df.count()
            column_count = len(df.columns)
            
            print(f"\n📋 {description} ({file_name}):")
            print(f"   Records: {record_count:,}")
            print(f"   Columns: {column_count}")
            print(f"   Schema: {', '.join(df.columns[:5])}{'...' if column_count > 5 else ''}")
            
            # Show sample data
            print(f"   Sample data:")
            df.show(3, truncate=True)
            
        except Exception as e:
            print(f"❌ Error reading {file_name}: {str(e)}")

# Inspect sample data
inspect_sample_data()

# COMMAND ----------

# MAGIC %md
# MAGIC ## 5. Schema and Catalog Validation

# COMMAND ----------

def validate_schemas():
    """Validate that required schemas exist"""
    print("🏗️ Schema Validation:")
    print("=" * 50)
    
    schemas_to_check = [
        volume_config["data_schema"],
        volume_config["bronze_schema"],
        volume_config["silver_schema"],
        volume_config["gold_schema"]
    ]
    
    for schema_name in schemas_to_check:
        try:
            # Check if schema exists
            spark.sql(f"DESCRIBE SCHEMA {schema_name}")
            print(f"   ✅ {schema_name} - EXISTS")
        except Exception as e:
            print(f"   ❌ {schema_name} - NOT FOUND")
            print(f"      Error: {str(e)}")

# Validate schemas
validate_schemas()

# COMMAND ----------

# MAGIC %md
# MAGIC ## 6. Create Volume Configuration Table

# COMMAND ----------

def create_volume_config_table():
    """Create a configuration table to store volume paths and settings"""
    
    config_data = [
        ("catalog_name", volume_config["catalog_name"]),
        ("data_schema", volume_config["data_schema"]),
        ("bronze_schema", volume_config["bronze_schema"]),
        ("silver_schema", volume_config["silver_schema"]),
        ("gold_schema", volume_config["gold_schema"]),
        ("source_data_path", volume_config["source_data_path"]),
        ("checkpoint_path", volume_config["checkpoint_path"]),
        ("volume_name", volume_config["volume_name"])
    ]
    
    config_df = spark.createDataFrame(
        config_data,
        ["config_key", "config_value"]
    ).withColumn("created_timestamp", current_timestamp())
    
    # Write configuration table
    config_df.write \
        .format("delta") \
        .mode("overwrite") \
        .option("mergeSchema", "true") \
        .saveAsTable(f"{volume_config['data_schema']}.pipeline_config")
    
    print("✅ Created pipeline configuration table")
    print(f"   Location: {volume_config['data_schema']}.pipeline_config")
    
    # Show configuration
    spark.table(f"{volume_config['data_schema']}.pipeline_config").show(truncate=False)

# Create configuration table
create_volume_config_table()

# COMMAND ----------

# MAGIC %md
# MAGIC ## 7. Set Spark Configuration

# COMMAND ----------

# Store configuration in Spark conf for access across notebooks
for key, value in volume_config.items():
    spark.conf.set(f"ecommerce.config.{key}", str(value))

# Store data sources list
data_sources_list = ",".join([
    "olist_customers_dataset",
    "olist_geolocation_dataset", 
    "olist_order_items_dataset",
    "olist_order_payments_dataset",
    "olist_order_reviews_dataset",
    "olist_orders_dataset",
    "olist_products_dataset",
    "olist_sellers_dataset",
    "product_category_name_translation"
])

spark.conf.set("ecommerce.config.data_sources", data_sources_list)

print("⚙️ Spark Configuration Set:")
print("   All volume paths and settings stored in Spark conf")
print("   Access with: spark.conf.get('ecommerce.config.{key}')")

# COMMAND ----------

# MAGIC %md
# MAGIC ## 8. Volume Setup Summary

# COMMAND ----------

print("📊 VOLUME SETUP VALIDATION SUMMARY")
print("=" * 60)
print(f"Catalog: {volume_config['catalog_name']}")
print(f"Data Schema: {volume_config['data_schema']}")
print(f"Volume: {volume_config['volume_name']}")
print()
print("📁 Paths:")
print(f"   Source Data: {volume_config['source_data_path']}")
print(f"   Checkpoints: {volume_config['checkpoint_path']}")
print()
print("🏗️ Schemas:")
print(f"   Bronze: {volume_config['bronze_schema']}")
print(f"   Silver: {volume_config['silver_schema']}")
print(f"   Gold: {volume_config['gold_schema']}")
print()

# Validation status
validation_status = {
    "Volume Paths": data_exists and checkpoint_exists,
    "Data Files": files_valid,
    "Configuration": True
}

print("✅ Validation Results:")
for check, status in validation_status.items():
    status_icon = "✅" if status else "❌"
    print(f"   {status_icon} {check}")

all_valid = all(validation_status.values())
print(f"\n🎯 Overall Status: {'READY FOR PIPELINE' if all_valid else 'NEEDS ATTENTION'}")

if all_valid:
    print("\n📋 Next Steps:")
    print("   1. Run: 00_setup_catalog_schemas.py")
    print("   2. Run: bronze/01_bronze_data_ingestion.py")
    print("   3. Continue with silver and gold layers")
else:
    print("\n⚠️  Please resolve validation issues before proceeding")

# COMMAND ----------

# MAGIC %md
# MAGIC ## 9. Quick Data Quality Check

# COMMAND ----------

def quick_data_quality_check():
    """Perform quick data quality checks on source files"""
    print("🔍 Quick Data Quality Check:")
    print("=" * 50)
    
    if not files_valid:
        print("❌ Cannot perform quality check - files validation failed")
        return
    
    quality_results = []
    
    # Check key files for basic quality
    key_files = [
        ("olist_customers_dataset.csv", "customer_id"),
        ("olist_orders_dataset.csv", "order_id"),
        ("olist_order_items_dataset.csv", "order_id"),
        ("olist_products_dataset.csv", "product_id")
    ]
    
    for file_name, key_column in key_files:
        try:
            file_path = volume_config["source_data_path"] + file_name
            df = spark.read.option("header", "true") \
                          .option("inferSchema", "true") \
                          .csv(file_path)
            
            total_records = df.count()
            null_keys = df.filter(col(key_column).isNull()).count()
            duplicate_keys = df.count() - df.select(key_column).distinct().count()
            
            quality_score = 100
            if null_keys > 0:
                quality_score -= (null_keys / total_records) * 50
            if duplicate_keys > 0:
                quality_score -= (duplicate_keys / total_records) * 30
            
            quality_results.append({
                "file": file_name,
                "records": total_records,
                "null_keys": null_keys,
                "duplicates": duplicate_keys,
                "quality_score": round(quality_score, 1)
            })
            
            print(f"\n📋 {file_name}:")
            print(f"   Records: {total_records:,}")
            print(f"   Null {key_column}: {null_keys}")
            print(f"   Duplicate {key_column}: {duplicate_keys}")
            print(f"   Quality Score: {quality_score:.1f}%")
            
        except Exception as e:
            print(f"❌ Error checking {file_name}: {str(e)}")
    
    # Overall quality summary
    if quality_results:
        avg_quality = sum([r["quality_score"] for r in quality_results]) / len(quality_results)
        print(f"\n📊 Overall Data Quality Score: {avg_quality:.1f}%")
        
        if avg_quality >= 95:
            print("🎉 Excellent data quality!")
        elif avg_quality >= 85:
            print("✅ Good data quality")
        elif avg_quality >= 70:
            print("⚠️  Acceptable data quality - monitor closely")
        else:
            print("❌ Poor data quality - investigate issues")

# Perform quality check
quick_data_quality_check()

# COMMAND ----------

print("🎉 Volume setup and validation completed!")
print("📋 Configuration saved and ready for pipeline execution")
