# Brazilian E-commerce Dataset Analysis

## Overview
This repository contains a comprehensive analysis of Brazilian e-commerce data from Olist, a Brazilian marketplace that connects small businesses to major marketplaces. The dataset provides real commercial data spanning from 2016 to 2018, offering insights into customer behavior, seller performance, product categories, and logistics operations across Brazil.

**Dataset Source**: [Brazilian E-Commerce Public Dataset by Olist](https://www.kaggle.com/datasets/olistbr/brazilian-ecommerce)

## Dataset Structure

The dataset consists of 9 interconnected CSV files containing **~1.1 million records** across various business dimensions:

### Core Business Entities

#### 1. **Orders Dataset** (`olist_orders_dataset.csv`)
- **Records**: 99,441 orders
- **Description**: Central fact table containing order lifecycle information
- **Key Columns**:
  - `order_id` (Primary Key): Unique identifier for each order
  - `customer_id` (Foreign Key): Links to customer information
  - `order_status`: Current status (delivered, shipped, canceled, etc.)
  - `order_purchase_timestamp`: When the order was placed
  - `order_approved_at`: When payment was approved
  - `order_delivered_carrier_date`: When handed to logistics partner
  - `order_delivered_customer_date`: When delivered to customer
  - `order_estimated_delivery_date`: Promised delivery date

#### 2. **Customers Dataset** (`olist_customers_dataset.csv`)
- **Records**: 99,441 customers
- **Description**: Customer demographic and location information
- **Key Columns**:
  - `customer_id` (Primary Key): Unique identifier for each customer
  - `customer_unique_id`: Unique identifier across the platform
  - `customer_zip_code_prefix`: ZIP code for location analysis
  - `customer_city`: Customer's city
  - `customer_state`: Customer's state (Brazilian state codes)

#### 3. **Order Items Dataset** (`olist_order_items_dataset.csv`)
- **Records**: 112,650 order items
- **Description**: Individual items within each order (order line items)
- **Key Columns**:
  - `order_id` (Foreign Key): Links to orders
  - `order_item_id`: Sequential number identifying items within an order
  - `product_id` (Foreign Key): Links to product information
  - `seller_id` (Foreign Key): Links to seller information
  - `shipping_limit_date`: Seller's shipping deadline
  - `price`: Item price in Brazilian Reais (BRL)
  - `freight_value`: Shipping cost for the item

#### 4. **Products Dataset** (`olist_products_dataset.csv`)
- **Records**: 32,951 products
- **Description**: Product catalog with physical and categorical attributes
- **Key Columns**:
  - `product_id` (Primary Key): Unique identifier for each product
  - `product_category_name`: Product category in Portuguese
  - `product_name_lenght`: Length of product name (characters)
  - `product_description_lenght`: Length of product description
  - `product_photos_qty`: Number of product photos
  - `product_weight_g`: Product weight in grams
  - `product_length_cm`, `product_height_cm`, `product_width_cm`: Dimensions

#### 5. **Sellers Dataset** (`olist_sellers_dataset.csv`)
- **Records**: 3,095 sellers
- **Description**: Seller information and geographic location
- **Key Columns**:
  - `seller_id` (Primary Key): Unique identifier for each seller
  - `seller_zip_code_prefix`: Seller's ZIP code
  - `seller_city`: Seller's city
  - `seller_state`: Seller's state

### Transaction and Interaction Data

#### 6. **Order Payments Dataset** (`olist_order_payments_dataset.csv`)
- **Records**: 103,886 payment records
- **Description**: Payment information for orders (orders can have multiple payments)
- **Key Columns**:
  - `order_id` (Foreign Key): Links to orders
  - `payment_sequential`: Sequential number for multiple payments
  - `payment_type`: Payment method (credit_card, boleto, voucher, debit_card)
  - `payment_installments`: Number of installments
  - `payment_value`: Payment amount in BRL

#### 7. **Order Reviews Dataset** (`olist_order_reviews_dataset.csv`)
- **Records**: 104,164 reviews
- **Description**: Customer reviews and ratings for orders
- **Key Columns**:
  - `review_id` (Primary Key): Unique identifier for each review
  - `order_id` (Foreign Key): Links to orders
  - `review_score`: Rating from 1 to 5 stars
  - `review_comment_title`: Review title
  - `review_comment_message`: Review text content
  - `review_creation_date`: When review was created
  - `review_answer_timestamp`: When seller responded

### Geographic and Reference Data

#### 8. **Geolocation Dataset** (`olist_geolocation_dataset.csv`)
- **Records**: 1,000,163 geolocation records
- **Description**: Geographic coordinates for Brazilian ZIP codes
- **Key Columns**:
  - `geolocation_zip_code_prefix`: ZIP code prefix
  - `geolocation_lat`: Latitude coordinate
  - `geolocation_lng`: Longitude coordinate
  - `geolocation_city`: City name
  - `geolocation_state`: State code

#### 9. **Product Category Translation** (`product_category_name_translation.csv`)
- **Records**: 71 category translations
- **Description**: Translation of product categories from Portuguese to English
- **Key Columns**:
  - `product_category_name`: Category name in Portuguese
  - `product_category_name_english`: Category name in English

## Data Relationships

The dataset follows a star schema design with `orders` as the central fact table:

```
customers ──┐
            ├── orders ──┬── order_items ──┬── products
sellers ────┘            │                  └── product_categories
                         ├── order_payments
                         └── order_reviews

geolocation (supports both customers and sellers via ZIP codes)
```

## Key Business Metrics Available

### Customer Analytics
- Customer acquisition and retention patterns
- Geographic distribution of customers
- Customer lifetime value analysis
- Purchase frequency and seasonality

### Product Performance
- Best-selling products and categories
- Product dimension impact on sales
- Category performance analysis
- Inventory turnover insights

### Seller Analytics
- Seller performance metrics
- Geographic distribution of sellers
- Seller-customer distance analysis
- Top-performing sellers by region

### Operational Metrics
- Order fulfillment times
- Delivery performance vs. estimates
- Payment method preferences
- Customer satisfaction scores

### Financial Analysis
- Revenue trends and seasonality
- Average order value analysis
- Payment installment patterns
- Freight cost optimization opportunities

## Data Quality Considerations

- **Completeness**: Some orders may have missing delivery dates (canceled/pending orders)
- **Consistency**: Product categories are in Portuguese (translation table provided)
- **Accuracy**: Geolocation data may have multiple entries per ZIP code
- **Timeliness**: Data covers 2016-2018 period

## Getting Started

1. **Data Exploration**: Start with the orders dataset to understand the business flow
2. **Relationship Mapping**: Use foreign keys to join related tables
3. **Geographic Analysis**: Leverage geolocation data for spatial insights
4. **Time Series Analysis**: Utilize timestamp fields for trend analysis

## Use Cases

This dataset is ideal for:
- E-commerce analytics and KPI development
- Customer segmentation and behavior analysis
- Supply chain and logistics optimization
- Market basket analysis
- Predictive modeling for demand forecasting
- Geographic expansion strategy
- Seller performance evaluation
- Customer satisfaction analysis

---

*This dataset provides a comprehensive view of Brazilian e-commerce operations, enabling deep insights into customer behavior, operational efficiency, and market dynamics.*
