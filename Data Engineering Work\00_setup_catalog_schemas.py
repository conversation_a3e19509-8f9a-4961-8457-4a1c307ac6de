# Databricks notebook source
# MAGIC %md
# MAGIC # Brazilian E-commerce Data Pipeline - Catalog & Schema Setup
# MAGIC 
# MAGIC This notebook sets up the Unity Catalog structure for the Brazilian e-commerce data pipeline using the Medallion Architecture.
# MAGIC 
# MAGIC **Architecture:**
# MAGIC - **Bronze Layer**: Raw data ingestion with minimal transformation
# MAGIC - **Silver Layer**: Cleaned and curated data for business use
# MAGIC - **Gold Layer**: Business-ready aggregated tables for analytics, data science, and ML

# COMMAND ----------

# MAGIC %md
# MAGIC ## 1. Import Required Libraries

# COMMAND ----------

from pyspark.sql import SparkSession
from pyspark.sql.functions import *
from pyspark.sql.types import *
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# COMMAND ----------

# MAGIC %md
# MAGIC ## 2. Create Unity Catalog and Schemas

# COMMAND ----------

# MAGIC %sql
# MAGIC -- Create the main catalog for e-commerce data
# MAGIC CREATE CATALOG IF NOT EXISTS ecommerce
# MAGIC COMMENT 'Brazilian E-commerce data catalog containing bronze, silver, and gold layers';

# COMMAND ----------

# MAGIC %sql
# MAGIC -- Use the ecommerce catalog
# MAGIC USE CATALOG ecommerce;

# COMMAND ----------

# MAGIC %sql
# MAGIC -- Create Bronze Schema (Raw Data Layer)
# MAGIC CREATE SCHEMA IF NOT EXISTS ecommerce.bronze
# MAGIC COMMENT 'Bronze layer containing raw data from source systems with minimal transformation. Data is stored as-is for historical reference and reprocessing capabilities.';

# COMMAND ----------

# MAGIC %sql
# MAGIC -- Create Silver Schema (Cleaned Data Layer)
# MAGIC CREATE SCHEMA IF NOT EXISTS ecommerce.silver
# MAGIC COMMENT 'Silver layer containing cleaned, validated, and curated data. Business rules applied, data quality issues resolved, and standardized formats implemented.';

# COMMAND ----------

# MAGIC %sql
# MAGIC -- Create Gold Schema (Business Ready Layer)
# MAGIC CREATE SCHEMA IF NOT EXISTS ecommerce.gold
# MAGIC COMMENT 'Gold layer containing business-ready aggregated tables optimized for analytics, reporting, data science, and machine learning workloads.';

# COMMAND ----------

# MAGIC %md
# MAGIC ## 3. Set Database Permissions and Properties

# COMMAND ----------

# MAGIC %sql
# MAGIC -- Set catalog properties
# MAGIC ALTER CATALOG ecommerce SET TAGS ('environment' = 'production', 'project' = 'brazilian-ecommerce', 'owner' = 'data-engineering-team');

# COMMAND ----------

# MAGIC %sql
# MAGIC -- Set schema properties for bronze layer
# MAGIC ALTER SCHEMA ecommerce.bronze SET TAGS ('layer' = 'bronze', 'data_classification' = 'raw', 'retention_days' = '2555'); -- 7 years retention

# COMMAND ----------

# MAGIC %sql
# MAGIC -- Set schema properties for silver layer
# MAGIC ALTER SCHEMA ecommerce.silver SET TAGS ('layer' = 'silver', 'data_classification' = 'curated', 'retention_days' = '1825'); -- 5 years retention

# COMMAND ----------

# MAGIC %sql
# MAGIC -- Set schema properties for gold layer
# MAGIC ALTER SCHEMA ecommerce.gold SET TAGS ('layer' = 'gold', 'data_classification' = 'business_ready', 'retention_days' = '1095'); -- 3 years retention

# COMMAND ----------

# MAGIC %md
# MAGIC ## 4. Verify Catalog Structure

# COMMAND ----------

# MAGIC %sql
# MAGIC -- Show all schemas in the ecommerce catalog
# MAGIC SHOW SCHEMAS IN ecommerce;

# COMMAND ----------

# MAGIC %sql
# MAGIC -- Describe the catalog
# MAGIC DESCRIBE CATALOG ecommerce;

# COMMAND ----------

# MAGIC %md
# MAGIC ## 5. Create Configuration Variables

# COMMAND ----------

# Configuration for the data pipeline
config = {
    "catalog_name": "ecommerce",
    "bronze_schema": "ecommerce.bronze",
    "silver_schema": "ecommerce.silver", 
    "gold_schema": "ecommerce.gold",
    "source_data_path": "/FileStore/shared_uploads/brazilian_ecommerce/",
    "checkpoint_path": "/tmp/checkpoints/brazilian_ecommerce/",
    "data_sources": [
        "olist_customers_dataset",
        "olist_geolocation_dataset", 
        "olist_order_items_dataset",
        "olist_order_payments_dataset",
        "olist_order_reviews_dataset",
        "olist_orders_dataset",
        "olist_products_dataset",
        "olist_sellers_dataset",
        "product_category_name_translation"
    ]
}

# Store configuration in Spark conf for access across notebooks
for key, value in config.items():
    if isinstance(value, list):
        spark.conf.set(f"ecommerce.config.{key}", ",".join(value))
    else:
        spark.conf.set(f"ecommerce.config.{key}", str(value))

print("✅ Catalog and schema setup completed successfully!")
print(f"📊 Created catalog: {config['catalog_name']}")
print(f"🥉 Bronze schema: {config['bronze_schema']}")
print(f"🥈 Silver schema: {config['silver_schema']}")
print(f"🥇 Gold schema: {config['gold_schema']}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## 6. Data Pipeline Architecture Overview
# MAGIC 
# MAGIC ```
# MAGIC ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
# MAGIC │   BRONZE LAYER  │    │  SILVER LAYER   │    │   GOLD LAYER    │
# MAGIC │   (Raw Data)    │───▶│ (Curated Data)  │───▶│ (Business Ready)│
# MAGIC └─────────────────┘    └─────────────────┘    └─────────────────┘
# MAGIC │                      │                      │
# MAGIC │ • Raw CSV ingestion  │ • Data cleaning      │ • Aggregated KPIs
# MAGIC │ • Minimal transform  │ • Business rules     │ • ML feature store
# MAGIC │ • Historical archive │ • Data validation    │ • Analytics tables
# MAGIC │ • Schema evolution   │ • Standardization    │ • Reporting views
# MAGIC └──────────────────────┴──────────────────────┴─────────────────────
# MAGIC ```
# MAGIC 
# MAGIC **Next Steps:**
# MAGIC 1. Run bronze layer data ingestion notebooks
# MAGIC 2. Execute silver layer data cleaning and curation
# MAGIC 3. Create gold layer business tables for analytics and ML
# MAGIC 4. Set up automated data pipeline workflows
