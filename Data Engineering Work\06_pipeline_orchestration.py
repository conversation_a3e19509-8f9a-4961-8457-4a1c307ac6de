# Databricks notebook source
# MAGIC %md
# MAGIC # Data Pipeline Orchestration and Automation
# MAGIC 
# MAGIC This notebook provides orchestration workflows and data quality monitoring for the Brazilian e-commerce data pipeline.
# MAGIC It includes automated workflows, data quality checks, and monitoring capabilities.

# COMMAND ----------

# MAGIC %md
# MAGIC ## 1. Setup and Configuration

# COMMAND ----------

from pyspark.sql import SparkSession
from pyspark.sql.functions import *
from pyspark.sql.types import *
from delta.tables import DeltaTable
import logging
from datetime import datetime, timedelta
import json

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get configuration
catalog_name = spark.conf.get("ecommerce.config.catalog_name", "ecommerce")
bronze_schema = spark.conf.get("ecommerce.config.bronze_schema", "ecommerce.bronze")
silver_schema = spark.conf.get("ecommerce.config.silver_schema", "ecommerce.silver")
gold_schema = spark.conf.get("ecommerce.config.gold_schema", "ecommerce.gold")

# Use the ecommerce catalog
spark.sql(f"USE CATALOG {catalog_name}")

print(f"🔧 Pipeline orchestration for catalog: {catalog_name}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## 2. Data Quality Framework

# COMMAND ----------

class DataQualityChecker:
    """Data quality validation framework for the medallion architecture"""
    
    def __init__(self, spark_session):
        self.spark = spark_session
        self.quality_results = []
    
    def check_table_exists(self, table_name):
        """Check if table exists"""
        try:
            self.spark.table(table_name).limit(1).collect()
            return {"check": "table_exists", "table": table_name, "status": "PASS", "message": "Table exists"}
        except Exception as e:
            return {"check": "table_exists", "table": table_name, "status": "FAIL", "message": str(e)}
    
    def check_record_count(self, table_name, min_records=1):
        """Check minimum record count"""
        try:
            count = self.spark.table(table_name).count()
            status = "PASS" if count >= min_records else "FAIL"
            message = f"Record count: {count:,} (minimum: {min_records:,})"
            return {"check": "record_count", "table": table_name, "status": status, "message": message, "count": count}
        except Exception as e:
            return {"check": "record_count", "table": table_name, "status": "FAIL", "message": str(e), "count": 0}
    
    def check_null_percentage(self, table_name, column_name, max_null_percentage=10):
        """Check null percentage in critical columns"""
        try:
            df = self.spark.table(table_name)
            total_count = df.count()
            null_count = df.filter(col(column_name).isNull()).count()
            null_percentage = (null_count / total_count) * 100 if total_count > 0 else 0
            
            status = "PASS" if null_percentage <= max_null_percentage else "FAIL"
            message = f"Null percentage: {null_percentage:.2f}% (max allowed: {max_null_percentage}%)"
            
            return {
                "check": "null_percentage", 
                "table": table_name, 
                "column": column_name,
                "status": status, 
                "message": message, 
                "null_percentage": null_percentage
            }
        except Exception as e:
            return {"check": "null_percentage", "table": table_name, "column": column_name, "status": "FAIL", "message": str(e)}
    
    def check_data_freshness(self, table_name, timestamp_column, max_age_hours=24):
        """Check data freshness"""
        try:
            df = self.spark.table(table_name)
            latest_timestamp = df.agg(max(timestamp_column)).collect()[0][0]
            
            if latest_timestamp:
                age_hours = (datetime.now() - latest_timestamp).total_seconds() / 3600
                status = "PASS" if age_hours <= max_age_hours else "FAIL"
                message = f"Data age: {age_hours:.1f} hours (max allowed: {max_age_hours} hours)"
            else:
                status = "FAIL"
                message = "No timestamp data found"
                age_hours = None
            
            return {
                "check": "data_freshness",
                "table": table_name,
                "status": status,
                "message": message,
                "age_hours": age_hours
            }
        except Exception as e:
            return {"check": "data_freshness", "table": table_name, "status": "FAIL", "message": str(e)}
    
    def check_referential_integrity(self, child_table, parent_table, child_key, parent_key):
        """Check referential integrity between tables"""
        try:
            child_df = self.spark.table(child_table).select(child_key).distinct()
            parent_df = self.spark.table(parent_table).select(parent_key).distinct()
            
            orphaned_records = child_df.join(parent_df, col(child_key) == col(parent_key), "left_anti").count()
            total_child_records = child_df.count()
            
            status = "PASS" if orphaned_records == 0 else "FAIL"
            message = f"Orphaned records: {orphaned_records:,} out of {total_child_records:,}"
            
            return {
                "check": "referential_integrity",
                "child_table": child_table,
                "parent_table": parent_table,
                "status": status,
                "message": message,
                "orphaned_records": orphaned_records
            }
        except Exception as e:
            return {"check": "referential_integrity", "child_table": child_table, "parent_table": parent_table, "status": "FAIL", "message": str(e)}
    
    def run_quality_suite(self):
        """Run comprehensive data quality checks"""
        
        # Bronze layer checks
        bronze_tables = [
            "customers", "orders", "order_items", "products", "sellers",
            "order_payments", "order_reviews", "geolocation", "product_category_translation"
        ]
        
        for table in bronze_tables:
            table_name = f"{bronze_schema}.{table}"
            self.quality_results.append(self.check_table_exists(table_name))
            self.quality_results.append(self.check_record_count(table_name, min_records=100))
        
        # Silver layer checks
        silver_tables = [
            "dim_customers", "dim_products", "dim_sellers", "fact_orders",
            "fact_order_items", "customer_payment_behavior", "product_performance", "seller_performance"
        ]
        
        for table in silver_tables:
            table_name = f"{silver_schema}.{table}"
            self.quality_results.append(self.check_table_exists(table_name))
            self.quality_results.append(self.check_record_count(table_name, min_records=100))
        
        # Gold layer checks
        gold_tables = [
            "daily_sales_summary", "customer_segmentation", "category_performance", "geographic_performance",
            "ml_customer_churn_features", "ml_demand_forecasting_features", "ml_recommendation_features", "ml_seller_performance_features"
        ]
        
        for table in gold_tables:
            table_name = f"{gold_schema}.{table}"
            self.quality_results.append(self.check_table_exists(table_name))
            self.quality_results.append(self.check_record_count(table_name, min_records=10))
        
        # Critical column null checks
        critical_checks = [
            (f"{bronze_schema}.orders", "order_id"),
            (f"{bronze_schema}.customers", "customer_id"),
            (f"{silver_schema}.fact_orders", "order_key"),
            (f"{silver_schema}.dim_customers", "customer_key")
        ]
        
        for table_name, column_name in critical_checks:
            self.quality_results.append(self.check_null_percentage(table_name, column_name, max_null_percentage=0))
        
        # Referential integrity checks
        integrity_checks = [
            (f"{silver_schema}.fact_orders", f"{silver_schema}.dim_customers", "customer_key", "customer_key"),
            (f"{silver_schema}.fact_order_items", f"{silver_schema}.fact_orders", "order_key", "order_key"),
            (f"{silver_schema}.fact_order_items", f"{silver_schema}.dim_products", "product_key", "product_key")
        ]
        
        for child_table, parent_table, child_key, parent_key in integrity_checks:
            self.quality_results.append(self.check_referential_integrity(child_table, parent_table, child_key, parent_key))
        
        return self.quality_results
    
    def generate_quality_report(self):
        """Generate data quality report"""
        if not self.quality_results:
            self.run_quality_suite()
        
        total_checks = len(self.quality_results)
        passed_checks = len([r for r in self.quality_results if r["status"] == "PASS"])
        failed_checks = total_checks - passed_checks
        
        print("📊 DATA QUALITY REPORT")
        print("=" * 50)
        print(f"Total Checks: {total_checks}")
        print(f"Passed: {passed_checks}")
        print(f"Failed: {failed_checks}")
        print(f"Success Rate: {(passed_checks/total_checks)*100:.1f}%")
        print()
        
        if failed_checks > 0:
            print("❌ FAILED CHECKS:")
            for result in self.quality_results:
                if result["status"] == "FAIL":
                    print(f"   - {result['check']}: {result.get('table', '')} - {result['message']}")
        else:
            print("✅ All quality checks passed!")
        
        return {
            "total_checks": total_checks,
            "passed_checks": passed_checks,
            "failed_checks": failed_checks,
            "success_rate": (passed_checks/total_checks)*100,
            "results": self.quality_results
        }

# Initialize data quality checker
dq_checker = DataQualityChecker(spark)

# COMMAND ----------

# MAGIC %md
# MAGIC ## 3. Pipeline Orchestration Functions

# COMMAND ----------

def run_bronze_layer_pipeline():
    """Execute bronze layer data ingestion"""
    try:
        logger.info("🥉 Starting bronze layer pipeline...")
        
        # This would typically call the bronze ingestion notebook
        # For demo purposes, we'll simulate the process
        
        # In production, you would use:
        # dbutils.notebook.run("./bronze/01_bronze_data_ingestion", timeout_seconds=3600)
        
        logger.info("✅ Bronze layer pipeline completed successfully")
        return {"status": "success", "layer": "bronze", "timestamp": datetime.now()}
    except Exception as e:
        logger.error(f"❌ Bronze layer pipeline failed: {str(e)}")
        return {"status": "failed", "layer": "bronze", "error": str(e), "timestamp": datetime.now()}

def run_silver_layer_pipeline():
    """Execute silver layer data cleaning and curation"""
    try:
        logger.info("🥈 Starting silver layer pipeline...")
        
        # In production, you would use:
        # dbutils.notebook.run("./silver/02_silver_data_cleaning", timeout_seconds=3600)
        # dbutils.notebook.run("./silver/03_silver_business_tables", timeout_seconds=3600)
        
        logger.info("✅ Silver layer pipeline completed successfully")
        return {"status": "success", "layer": "silver", "timestamp": datetime.now()}
    except Exception as e:
        logger.error(f"❌ Silver layer pipeline failed: {str(e)}")
        return {"status": "failed", "layer": "silver", "error": str(e), "timestamp": datetime.now()}

def run_gold_layer_pipeline():
    """Execute gold layer analytics and ML feature creation"""
    try:
        logger.info("🥇 Starting gold layer pipeline...")
        
        # In production, you would use:
        # dbutils.notebook.run("./gold/04_gold_analytics_tables", timeout_seconds=3600)
        # dbutils.notebook.run("./gold/05_gold_ml_feature_store", timeout_seconds=3600)
        
        logger.info("✅ Gold layer pipeline completed successfully")
        return {"status": "success", "layer": "gold", "timestamp": datetime.now()}
    except Exception as e:
        logger.error(f"❌ Gold layer pipeline failed: {str(e)}")
        return {"status": "failed", "layer": "gold", "error": str(e), "timestamp": datetime.now()}

def run_full_pipeline():
    """Execute the complete medallion architecture pipeline"""
    pipeline_results = []
    
    logger.info("🚀 Starting full medallion architecture pipeline...")
    
    # Run bronze layer
    bronze_result = run_bronze_layer_pipeline()
    pipeline_results.append(bronze_result)
    
    if bronze_result["status"] == "success":
        # Run silver layer
        silver_result = run_silver_layer_pipeline()
        pipeline_results.append(silver_result)
        
        if silver_result["status"] == "success":
            # Run gold layer
            gold_result = run_gold_layer_pipeline()
            pipeline_results.append(gold_result)
        else:
            logger.error("❌ Stopping pipeline due to silver layer failure")
    else:
        logger.error("❌ Stopping pipeline due to bronze layer failure")
    
    # Run data quality checks
    logger.info("🔍 Running data quality checks...")
    quality_report = dq_checker.generate_quality_report()
    
    # Generate pipeline summary
    successful_layers = len([r for r in pipeline_results if r["status"] == "success"])
    total_layers = len(pipeline_results)
    
    pipeline_summary = {
        "pipeline_start": pipeline_results[0]["timestamp"] if pipeline_results else datetime.now(),
        "pipeline_end": datetime.now(),
        "successful_layers": successful_layers,
        "total_layers": total_layers,
        "pipeline_success": successful_layers == 3,  # All three layers
        "quality_report": quality_report,
        "layer_results": pipeline_results
    }
    
    logger.info(f"🎉 Pipeline completed: {successful_layers}/{total_layers} layers successful")
    
    return pipeline_summary

# COMMAND ----------

# MAGIC %md
# MAGIC ## 4. Monitoring and Alerting

# COMMAND ----------

def create_pipeline_monitoring_table():
    """Create table to track pipeline execution history"""
    
    monitoring_schema = StructType([
        StructField("pipeline_run_id", StringType(), False),
        StructField("pipeline_start_time", TimestampType(), False),
        StructField("pipeline_end_time", TimestampType(), True),
        StructField("pipeline_status", StringType(), False),
        StructField("bronze_status", StringType(), True),
        StructField("silver_status", StringType(), True),
        StructField("gold_status", StringType(), True),
        StructField("quality_checks_passed", IntegerType(), True),
        StructField("quality_checks_failed", IntegerType(), True),
        StructField("quality_success_rate", DoubleType(), True),
        StructField("error_message", StringType(), True),
        StructField("created_timestamp", TimestampType(), False)
    ])
    
    # Create empty DataFrame with schema
    empty_df = spark.createDataFrame([], monitoring_schema)
    
    # Write to create table structure
    empty_df.write \
        .format("delta") \
        .mode("overwrite") \
        .option("mergeSchema", "true") \
        .saveAsTable(f"{gold_schema}.pipeline_monitoring")
    
    logger.info("✅ Created pipeline monitoring table")

def log_pipeline_execution(pipeline_summary):
    """Log pipeline execution results to monitoring table"""
    
    pipeline_run_id = f"run_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    # Extract layer statuses
    layer_results = {r["layer"]: r["status"] for r in pipeline_summary["layer_results"]}
    
    monitoring_data = [(
        pipeline_run_id,
        pipeline_summary["pipeline_start"],
        pipeline_summary["pipeline_end"],
        "SUCCESS" if pipeline_summary["pipeline_success"] else "FAILED",
        layer_results.get("bronze", "NOT_RUN"),
        layer_results.get("silver", "NOT_RUN"),
        layer_results.get("gold", "NOT_RUN"),
        pipeline_summary["quality_report"]["passed_checks"],
        pipeline_summary["quality_report"]["failed_checks"],
        pipeline_summary["quality_report"]["success_rate"],
        None,  # error_message
        datetime.now()
    )]
    
    monitoring_df = spark.createDataFrame(monitoring_data, [
        "pipeline_run_id", "pipeline_start_time", "pipeline_end_time", "pipeline_status",
        "bronze_status", "silver_status", "gold_status", "quality_checks_passed",
        "quality_checks_failed", "quality_success_rate", "error_message", "created_timestamp"
    ])
    
    # Append to monitoring table
    monitoring_df.write \
        .format("delta") \
        .mode("append") \
        .saveAsTable(f"{gold_schema}.pipeline_monitoring")
    
    logger.info(f"📝 Logged pipeline execution: {pipeline_run_id}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## 5. Execute Pipeline and Monitoring

# COMMAND ----------

# Create monitoring infrastructure
create_pipeline_monitoring_table()

# Run data quality checks
print("🔍 Running comprehensive data quality checks...")
quality_report = dq_checker.generate_quality_report()

# COMMAND ----------

# MAGIC %md
# MAGIC ## 6. Pipeline Configuration and Scheduling

# COMMAND ----------

# Pipeline configuration
pipeline_config = {
    "schedule": {
        "bronze_layer": "0 2 * * *",  # Daily at 2 AM
        "silver_layer": "0 4 * * *",  # Daily at 4 AM
        "gold_layer": "0 6 * * *",    # Daily at 6 AM
        "quality_checks": "0 8 * * *" # Daily at 8 AM
    },
    "retry_policy": {
        "max_retries": 3,
        "retry_delay_minutes": 15
    },
    "alerting": {
        "email_recipients": ["<EMAIL>"],
        "slack_webhook": "https://hooks.slack.com/services/...",
        "alert_on_failure": True,
        "alert_on_quality_issues": True
    },
    "data_retention": {
        "bronze_days": 2555,  # 7 years
        "silver_days": 1825,  # 5 years
        "gold_days": 1095     # 3 years
    }
}

# Store configuration
spark.conf.set("ecommerce.pipeline.config", json.dumps(pipeline_config))

print("⚙️ Pipeline configuration saved")
print(f"📅 Scheduled runs:")
for layer, schedule in pipeline_config["schedule"].items():
    print(f"   - {layer}: {schedule}")

# COMMAND ----------

print("🎉 Data pipeline orchestration setup completed successfully!")
print(f"\n📊 Pipeline Components:")
print("   ✅ Data quality framework implemented")
print("   ✅ Pipeline orchestration functions created")
print("   ✅ Monitoring and alerting configured")
print("   ✅ Scheduling configuration defined")
print(f"\n📋 Next steps:")
print("   1. Set up Databricks Jobs for automated scheduling")
print("   2. Configure alerting integrations (email, Slack)")
print("   3. Implement incremental data processing")
print("   4. Set up performance monitoring dashboards")
print("   5. Create data lineage documentation")
