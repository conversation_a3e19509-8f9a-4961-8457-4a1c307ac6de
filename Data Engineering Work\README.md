# Brazilian E-commerce Data Engineering Pipeline

## Overview

This project implements a comprehensive data engineering pipeline for Brazilian e-commerce data using the **Medallion Architecture** (Bronze-Silver-Gold) on **Azure Databricks** with **PySpark** and **Delta Lake**.

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   BRONZE LAYER  │    │  SILVER LAYER   │    │   GOLD LAYER    │
│   (Raw Data)    │───▶│ (Curated Data)  │───▶│ (Business Ready)│
└─────────────────┘    └─────────────────┘    └─────────────────┘
│                      │                      │
│ • Raw CSV ingestion  │ • Data cleaning      │ • Analytics tables
│ • Minimal transform  │ • Business rules     │ • ML feature store
│ • Historical archive │ • Data validation    │ • Aggregated KPIs
│ • Schema evolution   │ • Standardization    │ • Reporting views
└──────────────────────┴──────────────────────┴─────────────────────
```

## Technology Stack

- **Platform**: Azure Databricks
- **Processing Engine**: Apache Spark (PySpark)
- **Storage Format**: Delta Lake
- **Catalog**: Unity Catalog
- **Orchestration**: Databricks Workflows
- **Language**: Python/SQL

## Project Structure

```
Data Engineering Work/
├── 00_setup_catalog_schemas.py          # Catalog and schema setup
├── bronze/
│   └── 01_bronze_data_ingestion.py      # Raw data ingestion
├── silver/
│   ├── 02_silver_data_cleaning.py       # Data cleaning and curation
│   └── 03_silver_business_tables.py     # Business-specific tables
├── gold/
│   ├── 04_gold_analytics_tables.py      # Analytics-ready tables
│   └── 05_gold_ml_feature_store.py      # ML feature engineering
├── 06_pipeline_orchestration.py         # Workflow automation
└── README.md                            # This file
```

## Data Catalog Structure

### Unity Catalog: `ecommerce`

#### Bronze Schema: `ecommerce.bronze`
Raw data tables with minimal transformation:
- `customers` - Customer demographic information
- `orders` - Order lifecycle and status
- `order_items` - Individual items within orders
- `products` - Product catalog and attributes
- `sellers` - Seller information and location
- `order_payments` - Payment transaction details
- `order_reviews` - Customer reviews and ratings
- `geolocation` - Geographic coordinates for ZIP codes
- `product_category_translation` - Category translations

#### Silver Schema: `ecommerce.silver`
Cleaned and curated business tables:
- `dim_customers` - Customer dimension with data quality
- `dim_products` - Product dimension with enriched attributes
- `dim_sellers` - Seller dimension with regional classification
- `fact_orders` - Orders fact table with delivery metrics
- `fact_order_items` - Order items with business calculations
- `customer_payment_behavior` - Payment analysis by customer
- `product_performance` - Product sales and quality metrics
- `seller_performance` - Seller performance scorecards

#### Gold Schema: `ecommerce.gold`
Business-ready analytics and ML tables:

**Analytics Tables:**
- `daily_sales_summary` - Executive dashboard metrics
- `customer_segmentation` - RFM analysis and customer segments
- `category_performance` - Product category analytics
- `geographic_performance` - State-level market analysis

**ML Feature Store:**
- `ml_customer_churn_features` - Churn prediction features
- `ml_demand_forecasting_features` - Product demand forecasting
- `ml_recommendation_features` - Recommendation system features
- `ml_seller_performance_features` - Seller optimization features

**Monitoring:**
- `pipeline_monitoring` - Pipeline execution tracking

## Getting Started

### Prerequisites
- Azure Databricks workspace
- Unity Catalog enabled
- Appropriate permissions for catalog creation
- Source data files in accessible storage

### Setup Instructions

1. **Initialize Catalog and Schemas**
   ```python
   # Run the setup notebook
   %run ./00_setup_catalog_schemas
   ```

2. **Execute Bronze Layer**
   ```python
   # Ingest raw data
   %run ./bronze/01_bronze_data_ingestion
   ```

3. **Process Silver Layer**
   ```python
   # Clean and curate data
   %run ./silver/02_silver_data_cleaning
   %run ./silver/03_silver_business_tables
   ```

4. **Create Gold Layer**
   ```python
   # Generate analytics and ML tables
   %run ./gold/04_gold_analytics_tables
   %run ./gold/05_gold_ml_feature_store
   ```

5. **Set Up Orchestration**
   ```python
   # Configure monitoring and automation
   %run ./06_pipeline_orchestration
   ```

## Data Pipeline Features

### Bronze Layer
- **Raw Data Preservation**: Store data as-is for historical reference
- **Metadata Enrichment**: Add ingestion timestamps and lineage
- **Schema Evolution**: Handle changing data structures
- **Audit Trail**: Complete data lineage tracking

### Silver Layer
- **Data Quality**: Comprehensive validation and cleansing
- **Business Rules**: Apply domain-specific transformations
- **Standardization**: Consistent formats and naming conventions
- **Performance Optimization**: Partitioning and indexing strategies

### Gold Layer
- **Analytics Ready**: Pre-aggregated tables for fast queries
- **ML Features**: Engineered features for machine learning
- **Business KPIs**: Executive dashboard metrics
- **Self-Service**: User-friendly tables for business analysts

## Business Use Cases

### Analytics Team
- **Executive Dashboards**: Daily sales summaries and KPIs
- **Customer Analysis**: Segmentation and behavior insights
- **Market Intelligence**: Geographic and category performance
- **Operational Metrics**: Delivery and seller performance

### Data Science Team
- **Churn Prediction**: Customer retention modeling
- **Demand Forecasting**: Inventory optimization
- **Recommendation Systems**: Personalized product suggestions
- **Price Optimization**: Dynamic pricing strategies

### ML Engineering Team
- **Feature Store**: Centralized feature management
- **Model Training**: Ready-to-use training datasets
- **Real-time Scoring**: Low-latency feature serving
- **Experiment Tracking**: Model performance monitoring

## Data Quality Framework

### Automated Checks
- **Table Existence**: Verify all expected tables exist
- **Record Counts**: Minimum threshold validations
- **Null Percentages**: Critical column completeness
- **Referential Integrity**: Foreign key relationships
- **Data Freshness**: Timeliness monitoring

### Quality Metrics
- **Completeness**: Percentage of non-null values
- **Accuracy**: Data validation against business rules
- **Consistency**: Cross-table relationship validation
- **Timeliness**: Data freshness monitoring

## Performance Optimization

### Delta Lake Features
- **ACID Transactions**: Data consistency guarantees
- **Time Travel**: Historical data access
- **Schema Evolution**: Automatic schema updates
- **Compaction**: Storage optimization

### Spark Optimizations
- **Partitioning**: Efficient data organization
- **Caching**: In-memory performance
- **Broadcast Joins**: Small table optimizations
- **Adaptive Query Execution**: Dynamic optimization

## Monitoring and Alerting

### Pipeline Monitoring
- **Execution Tracking**: Success/failure rates
- **Performance Metrics**: Runtime and resource usage
- **Data Quality Scores**: Automated quality assessment
- **Error Handling**: Comprehensive error logging

### Alerting Configuration
- **Email Notifications**: Pipeline failure alerts
- **Slack Integration**: Real-time status updates
- **Dashboard Monitoring**: Visual pipeline health
- **SLA Tracking**: Service level agreement monitoring

## Deployment and Scheduling

### Databricks Jobs
- **Bronze Layer**: Daily at 2 AM
- **Silver Layer**: Daily at 4 AM  
- **Gold Layer**: Daily at 6 AM
- **Quality Checks**: Daily at 8 AM

### Retry Policy
- **Max Retries**: 3 attempts
- **Retry Delay**: 15 minutes
- **Exponential Backoff**: Progressive delay increase

## Security and Governance

### Unity Catalog Features
- **Fine-grained Access Control**: Table and column-level permissions
- **Data Lineage**: Automatic lineage tracking
- **Audit Logging**: Complete access audit trail
- **Data Classification**: Sensitive data tagging

### Compliance
- **LGPD Compliance**: Brazilian data protection law
- **Data Retention**: Automated lifecycle management
- **Encryption**: Data at rest and in transit
- **Access Controls**: Role-based permissions

## Contributing

1. Follow the medallion architecture principles
2. Implement comprehensive data quality checks
3. Document all transformations and business logic
4. Include performance optimization considerations
5. Maintain backward compatibility

## Support

For questions or issues:
- Review the pipeline monitoring dashboard
- Check the data quality reports
- Consult the Unity Catalog lineage
- Contact the data engineering team

---

**Last Updated**: October 2025  
**Version**: 1.0  
**Maintained by**: Data Engineering Team
